import asyncio
import cv2
import base64
import threading
import time
import uuid
import logging
from typing import Dict, Optional, Any, List
import numpy as np
from datetime import datetime, timedelta
import json
from collections import deque
import os
import subprocess
import tempfile
import atexit
import signal
import sys
import queue
import socket
import random
import http.server
import socketserver
import threading
import shutil
from queue import Queue, Empty

logger = logging.getLogger(__name__)

# 导入音频服务（延迟导入避免循环依赖）
def get_audio_service():
    from .audio_service import audio_service
    return audio_service

# 全局保存所有FFmpeg进程，在退出时清理
ffmpeg_processes = []
http_servers = []

def cleanup_resources():
    """清理所有FFmpeg进程和HTTP服务器"""
    for process in ffmpeg_processes:
        try:
            process.terminate()
            process.wait(timeout=1)
        except Exception as e:
            logger.error(f"清理FFmpeg进程失败: {e}")
            try:
                process.kill()
            except:
                pass
    
    for server in http_servers:
        try:
            server.shutdown()
            server.server_close()
        except Exception as e:
            logger.error(f"关闭HTTP服务器失败: {e}")

# 注册退出处理函数
atexit.register(cleanup_resources)
signal.signal(signal.SIGTERM, lambda sig, frame: (cleanup_resources(), sys.exit(0)))
signal.signal(signal.SIGINT, lambda sig, frame: (cleanup_resources(), sys.exit(0)))

def find_free_port():
    """查找可用的端口"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        return s.getsockname()[1]

class MJPEGHandler(http.server.BaseHTTPRequestHandler):
    """MJPEG HTTP处理器"""
    
    frames_queue = Queue(maxsize=10)
    active = True
    
    def do_HEAD(self):
        """处理HEAD请求"""
        if self.path == '/stream.mjpeg':
            self.send_response(200)
            self.send_header('Content-type', 'multipart/x-mixed-replace; boundary=--jpgboundary')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_GET(self):
        """处理GET请求，发送MJPEG流"""
        if self.path == '/stream.mjpeg':
            self.send_response(200)
            self.send_header('Content-type', 'multipart/x-mixed-replace; boundary=--jpgboundary')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
            
            try:
                while MJPEGHandler.active:
                    try:
                        # 尝试从队列获取帧，如果30秒内没有新帧则超时
                        frame = MJPEGHandler.frames_queue.get(timeout=30)
                        
                        # 发送帧
                        self.wfile.write(b"--jpgboundary\r\n")
                        self.wfile.write(b"Content-Type: image/jpeg\r\n")
                        self.wfile.write(f"Content-Length: {len(frame)}\r\n\r\n".encode())
                        self.wfile.write(frame)
                        self.wfile.write(b"\r\n")
                    except Empty:
                        # 队列超时，发送空帧保持连接
                        logger.warning("30秒内未收到新帧，可能连接有问题")
                        continue
                    except Exception as e:
                        logger.error(f"发送帧时出错: {e}")
                        break
            except ConnectionError:
                logger.info("客户端已断开连接")
            except Exception as e:
                logger.error(f"流处理错误: {e}")
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'404 - Not Found')
    
    def log_message(self, format, *args):
        """覆盖日志方法，减少输出"""
        return

class RTSPTranscoder:
    """改进的RTSP转HTTP MJPEG转码器"""
    
    def __init__(self, rtsp_url, http_port=None):
        self.rtsp_url = rtsp_url
        self.http_port = http_port or find_free_port()
        self.process = None
        self.is_running = False
        self.http_url = f"http://localhost:{self.http_port}/stream.mjpeg"
        self.temp_dir = tempfile.mkdtemp(prefix="rtsp_transcoder_")
        self.http_server = None
        self.server_thread = None
        self.latest_frame_data = None  # 保存最新帧数据
        
    def start_http_server(self):
        """启动HTTP服务器"""
        try:
            # 创建简单的HTTP服务器
            handler = MJPEGHandler
            # 重置队列状态
            while not MJPEGHandler.frames_queue.empty():
                try:
                    MJPEGHandler.frames_queue.get_nowait()
                except Empty:
                    pass
            MJPEGHandler.active = True
            
            self.http_server = socketserver.ThreadingTCPServer(("0.0.0.0", self.http_port), handler)
            
            # 在单独的线程中运行HTTP服务器
            self.server_thread = threading.Thread(target=self.http_server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            # 添加到全局HTTP服务器列表
            http_servers.append(self.http_server)
            
            logger.info(f"HTTP服务器已启动在端口 {self.http_port}")
            return True
        except Exception as e:
            logger.error(f"启动HTTP服务器失败: {e}")
            return False
    
    def start(self):
        """启动转码器"""
        if self.is_running:
            logger.warning("转码器已在运行")
            return self.http_url
        
        # 启动HTTP服务器
        if not self.start_http_server():
            return None
            
        # 构建FFmpeg命令 - 使用update参数，每次写入同一个文件
        cmd = [
            'ffmpeg',
            '-hide_banner',             # 隐藏版权信息
            '-loglevel', 'error',       # 只显示错误
            '-rtsp_transport', 'tcp',   # 使用TCP，更可靠
            '-i', self.rtsp_url,        # 输入RTSP流
            '-vf', 'scale=640:480',     # 缩放到较小分辨率
            '-r', '15',                 # 降低帧率为15fps
            '-q:v', '5',                # 较高质量
            '-an',                      # 不处理音频
            '-f', 'image2',             # 图像序列格式
            '-update', '1',             # 关键参数：覆盖同一个文件
            os.path.join(self.temp_dir, 'latest_frame.jpg')  # 固定文件名
        ]
        
        cmd_str = ' '.join(cmd)
        logger.info(f"启动兼容版RTSP转码器: {cmd_str}")
        
        try:
            # 启动FFmpeg进程
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 添加到全局进程列表
            ffmpeg_processes.append(self.process)
            
            # 启动错误监控线程
            self.error_thread = threading.Thread(target=self._monitor_errors)
            self.error_thread.daemon = True
            self.error_thread.start()
            
            # 启动帧读取线程
            self.frame_thread = threading.Thread(target=self._process_frames)
            self.frame_thread.daemon = True
            self.frame_thread.start()
            
            self.is_running = True
            logger.info(f"转码器启动成功: {self.rtsp_url} -> {self.http_url}")
            
            # 等待服务启动
            time.sleep(2)
            return self.http_url
            
        except Exception as e:
            logger.error(f"启动转码器失败: {e}")
            if self.process:
                self.process.terminate()
                if self.process in ffmpeg_processes:
                    ffmpeg_processes.remove(self.process)
                self.process = None
            return None
    
    def _process_frames(self):
        """处理生成的帧并放入队列"""
        frame_path = os.path.join(self.temp_dir, 'latest_frame.jpg')
        frame_count = 0
        last_frame_time = time.time()
        last_log_time = time.time()
        last_modified = 0
        
        logger.info(f"开始处理帧，监视文件: {frame_path}")
        
        while self.is_running:
            try:
                # 检查文件是否存在且被修改
                if os.path.exists(frame_path):
                    current_modified = os.path.getmtime(frame_path)
                    
                    # 如果文件被修改，读取并处理
                    if current_modified > last_modified:
                        last_modified = current_modified
                        
                        try:
                            # 读取帧文件
                            with open(frame_path, 'rb') as f:
                                frame_data = f.read()
                            
                            # 确保帧数据有效
                            if len(frame_data) > 100:  # 确保不是空文件
                                # 保存最新帧数据
                                self.latest_frame_data = frame_data
                                
                                # 如果队列已满，先移除旧帧
                                if MJPEGHandler.frames_queue.full():
                                    try:
                                        MJPEGHandler.frames_queue.get_nowait()
                                    except Empty:
                                        pass
                                
                                # 添加到队列
                                MJPEGHandler.frames_queue.put(frame_data)
                                frame_count += 1
                                
                                # 更新最后一帧时间
                                last_frame_time = time.time()
                                
                                # 每100帧记录一次
                                if frame_count % 100 == 0:
                                    logger.info(f"已处理 {frame_count} 帧")
                        except Exception as e:
                            logger.error(f"读取帧文件失败: {e}")
                elif time.time() - last_log_time > 5:
                    logger.warning(f"帧文件不存在: {frame_path}")
                    last_log_time = time.time()
                
                # 如果10秒没有新帧，发出警告
                if time.time() - last_frame_time > 10:
                    logger.warning("10秒内未收到新帧，检查RTSP源")
                    last_frame_time = time.time()  # 重置时间，避免重复警告
                
                # 休眠一小段时间
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"处理帧时出错: {e}")
                time.sleep(1)  # 错误后等待更长时间
    
    def _monitor_errors(self):
        """监控FFmpeg错误输出"""
        while self.is_running and self.process:
            line = self.process.stderr.readline()
            if not line:
                break
                
            line = line.decode('utf-8', errors='ignore').strip()
            if line:
                logger.error(f"FFmpeg错误: {line}")
                
        logger.info("错误监控线程结束")
    
    def get_latest_frame(self):
        """获取最新的帧数据"""
        return self.latest_frame_data
        
    def stop(self):
        """停止转码器"""
        self.is_running = False
        MJPEGHandler.active = False
        
        if self.process:
            logger.info("正在停止转码器...")
            try:
                self.process.terminate()
                self.process.wait(timeout=3)
                if self.process in ffmpeg_processes:
                    ffmpeg_processes.remove(self.process)
                logger.info("转码器已正常停止")
            except subprocess.TimeoutExpired:
                logger.warning("转码器未能正常停止，强制终止")
                self.process.kill()
                if self.process in ffmpeg_processes:
                    ffmpeg_processes.remove(self.process)
            except Exception as e:
                logger.error(f"停止转码器时出错: {e}")
            finally:
                self.process = None
        
        # 停止HTTP服务器
        if self.http_server:
            try:
                self.http_server.shutdown()
                self.http_server.server_close()
                if self.http_server in http_servers:
                    http_servers.remove(self.http_server)
                logger.info("HTTP服务器已停止")
            except Exception as e:
                logger.error(f"停止HTTP服务器时出错: {e}")
        
        # 清理临时目录
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"临时目录已清理: {self.temp_dir}")
        except Exception as e:
            logger.error(f"清理临时目录时出错: {e}")
        
        logger.info("转码器已停止")

class LocalStreamServer:
    """创建本地流服务器，将RTSP转为HTTP流"""
    
    def __init__(self, rtsp_url, port=None):
        self.rtsp_url = rtsp_url
        self.port = port or find_free_port()
        self.process = None
        self.is_running = False
        self.http_url = f"http://localhost:{self.port}/stream.mjpeg"
        
    def start(self):
        """启动本地流服务器"""
        try:
            # 构建FFmpeg命令进行转码 - 兼容版
            cmd = [
                'ffmpeg',
                '-hide_banner',
                '-loglevel', 'error',
                
                # RTSP连接选项
                '-rtsp_transport', 'tcp',
                
                # 输入缓冲和分析
                '-analyzeduration', '2000000',
                '-probesize', '1000000',
                '-fflags', '+genpts+discardcorrupt+igndts',
                
                # 输入流
                '-i', self.rtsp_url,
                
                # 转码选项
                '-vf', 'scale=640:480',   # 降低分辨率以减少处理负担
                '-r', '10',               # 降低帧率以提高稳定性
                '-q:v', '5',              # 较高质量
                '-c:v', 'mjpeg',          # 更容易解码的格式
                '-an',                    # 忽略音频
                '-f', 'mpjpeg',           # Motion JPEG格式
                
                # 输出到HTTP
                f'http://localhost:{self.port}/stream.mjpeg'
            ]
            
            logger.info(f"启动兼容型本地流服务: {' '.join(cmd)}")
            
            # 启动FFmpeg进程
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 添加到全局进程列表
            ffmpeg_processes.append(self.process)
            self.is_running = True
            
            # 等待服务启动
            time.sleep(3)  # 增加等待时间
            
            # 测试连接
            success = self._test_connection()
            if success:
                logger.info(f"本地流服务启动成功: {self.http_url}")
                return True
            else:
                # 检查进程错误
                if self.process:
                    stderr = self.process.stderr.read()
                    if stderr:
                        logger.error(f"本地流服务错误: {stderr.decode('utf-8', errors='ignore')}")
                
                self.stop()
                logger.error("本地流服务测试失败")
                return False
                
        except Exception as e:
            logger.error(f"启动本地流服务失败: {e}")
            if self.process:
                self.process.terminate()
                if self.process in ffmpeg_processes:
                    ffmpeg_processes.remove(self.process)
            return False
    
    def _test_connection(self):
        """测试HTTP连接是否可用"""
        try:
            import urllib.request
            req = urllib.request.Request(self.http_url)
            # 设置短超时
            response = urllib.request.urlopen(req, timeout=5)
            return response.code == 200
        except Exception as e:
            logger.error(f"测试HTTP连接失败: {e}")
            return False
    
    def stop(self):
        """停止本地流服务器"""
        self.is_running = False
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=2)
                if self.process in ffmpeg_processes:
                    ffmpeg_processes.remove(self.process)
            except Exception as e:
                logger.error(f"停止本地流服务失败: {e}")
                try:
                    self.process.kill()
                    if self.process in ffmpeg_processes:
                        ffmpeg_processes.remove(self.process)
                except:
                    pass
            finally:
                self.process = None
        
        logger.info(f"本地流服务已停止: {self.http_url}")

class RTSPStreamReader:
    """使用FFmpeg进行更可靠的RTSP流读取"""
    
    def __init__(self, rtsp_url, buffer_size=10, width=1280, height=720):
        self.rtsp_url = rtsp_url
        self.width = width
        self.height = height
        self.process = None
        self.is_running = False
        self.frame_buffer = queue.Queue(maxsize=buffer_size)
        self.last_frame = None
        
    def start(self):
        """启动FFmpeg流读取线程"""
        if self.is_running:
            return
            
        self.is_running = True
        self.thread = threading.Thread(target=self._read_stream)
        self.thread.daemon = True
        self.thread.start()
        logger.info(f"FFmpeg RTSP流读取器已启动: {self.rtsp_url}")
        return True
        
    def stop(self):
        """停止FFmpeg流读取"""
        self.is_running = False
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=2)
                logger.info("FFmpeg进程已终止")
            except Exception as e:
                logger.error(f"停止FFmpeg进程失败: {e}")
                try:
                    self.process.kill()
                except:
                    pass
            finally:
                self.process = None
                
        # 等待线程结束
        if hasattr(self, 'thread') and self.thread.is_alive():
            self.thread.join(timeout=3)
            
        logger.info("RTSP流读取器已停止")
        
    def _read_stream(self):
        """使用FFmpeg读取RTSP流并将帧放入缓冲区"""
        try:
            # 构建FFmpeg命令 - 修改为兼容性更好的选项
            cmd = [
                'ffmpeg',
                '-hide_banner',
                '-loglevel', 'error',
                
                # RTSP相关选项
                '-rtsp_transport', 'tcp',
                
                # 输入缓冲和超时选项
                '-analyzeduration', '2000000',  # 更长的分析时间
                '-probesize', '1000000',       # 更大的探测尺寸
                '-fflags', '+genpts+discardcorrupt+igndts',  # 忽略错误数据
                
                # 输入
                '-i', self.rtsp_url,
                
                # 解码选项 - 故障恢复模式
                '-vf', f'scale={self.width}:{self.height},fps=15',  # 缩放并降低帧率增加稳定性
                '-vsync', 'passthrough',   # 保持原始时间戳
                '-an',                     # 不处理音频
                
                # 输出格式
                '-c:v', 'rawvideo',
                '-pix_fmt', 'bgr24',       # OpenCV兼容格式
                '-f', 'rawvideo',
                '-'                        # 输出到stdout
            ]
            
            logger.info(f"启动兼容型FFmpeg进程: {' '.join(cmd)}")
            
            # 启动FFmpeg进程
            self.process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=10**8  # 大缓冲区
            )
            
            # 添加到全局进程列表
            ffmpeg_processes.append(self.process)
            
            frame_size = self.width * self.height * 3  # 3通道BGR
            
            # 循环读取FFmpeg输出
            while self.is_running:
                # 读取一帧大小的数据
                raw_data = self.process.stdout.read(frame_size)
                if len(raw_data) == 0:
                    logger.warning("FFmpeg流结束")
                    break
                    
                if len(raw_data) < frame_size:
                    logger.warning(f"不完整的帧数据: {len(raw_data)} < {frame_size}")
                    continue
                    
                try:
                    # 将原始字节转换为NumPy数组
                    frame = np.frombuffer(raw_data, dtype=np.uint8).reshape((self.height, self.width, 3))
                    
                    # 将帧放入缓冲区
                    try:
                        # 使用非阻塞方式，如果队列已满则丢弃旧的帧
                        if self.frame_buffer.full():
                            self.frame_buffer.get_nowait()  # 移除旧帧
                        self.frame_buffer.put_nowait(frame)
                        self.last_frame = frame
                    except queue.Full:
                        pass  # 忽略队列满的情况
                        
                except Exception as e:
                    logger.error(f"处理帧数据失败: {e}")
                    
            # 检查进程是否有错误
            stderr = self.process.stderr.read()
            if stderr:
                logger.error(f"FFmpeg错误: {stderr.decode('utf-8', errors='ignore')}")
                
        except Exception as e:
            logger.error(f"FFmpeg读取线程异常: {e}")
        finally:
            if self.process:
                try:
                    self.process.terminate()
                    if self.process in ffmpeg_processes:
                        ffmpeg_processes.remove(self.process)
                except:
                    pass
            logger.info("RTSP流读取线程已结束")
                
    def read(self):
        """读取一帧，类似于cv2.VideoCapture.read()"""
        if not self.is_running:
            return False, None
            
        try:
            # 尝试从缓冲区获取最新的帧，非阻塞
            frame = self.frame_buffer.get_nowait()
            return True, frame
        except queue.Empty:
            # 如果队列为空但有最后一帧，返回最后一帧
            if self.last_frame is not None:
                return True, self.last_frame.copy()
            return False, None
        except Exception as e:
            logger.error(f"读取帧失败: {e}")
            return False, None

class FrameQualityAnalyzer:
    """帧质量分析器"""
    
    @staticmethod
    def calculate_sharpness(frame: np.ndarray) -> float:
        """计算图像清晰度（拉普拉斯算子方差）"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            return laplacian.var()
        except Exception as e:
            logger.error(f"计算清晰度失败: {e}")
            return 0.0
    
    @staticmethod
    def calculate_brightness(frame: np.ndarray) -> float:
        """计算图像亮度"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            return np.mean(gray)
        except Exception as e:
            logger.error(f"计算亮度失败: {e}")
            return 0.0
    
    @staticmethod
    def select_best_frame(frames: List[np.ndarray]) -> Optional[np.ndarray]:
        """从多个帧中选择质量最好的一帧"""
        if not frames:
            return None
        
        best_frame = None
        best_score = -1
        
        for frame in frames:
            # 计算综合质量分数（清晰度权重更高）
            sharpness = FrameQualityAnalyzer.calculate_sharpness(frame)
            brightness = FrameQualityAnalyzer.calculate_brightness(frame)
            
            # 亮度在80-180之间为最佳，过暗或过亮都会扣分
            brightness_score = 1.0 - abs(brightness - 130) / 130
            brightness_score = max(0, brightness_score)
            
            # 综合分数：清晰度占80%，亮度占20%
            score = sharpness * 0.8 + brightness_score * 1000 * 0.2
            
            if score > best_score:
                best_score = score
                best_frame = frame
        
        logger.info(f"选择最佳帧，质量分数: {best_score:.2f}")
        return best_frame

class VideoStream:
    """单个视频流处理类"""
    
    def __init__(self, rtsp_url: str, session_id: str):
        self.rtsp_url = rtsp_url
        self.session_id = session_id
        self.cap = None
        self.ffmpeg_reader = None  # FFmpeg RTSP流读取器
        self.local_stream = None   # 本地流服务器
        self.transcoder = None     # 新增: RTSP转码器
        self.use_ffmpeg = True     # 默认使用FFmpeg
        self.is_running = False
        self.is_playing = False
        self.current_frame = None
        self.frame_lock = threading.Lock()
        self.thread = None
        self.last_frame_time = 0
        self.frame_count = 0
        self.fps = 15  # 降低fps以提高稳定性
        
        # 按需采样相关
        self.captured_frames = deque(maxlen=270)  # 存储9秒的帧(30fps * 9)
        self.frame_interval = 3  # 每3帧取一帧
        self.analysis_frames = []  # 用于分析的帧
        
        # 连接状态
        self.connection_status = "disconnected"
        self.last_error = None
        
    def start_video_display(self):
        """启动视频显示（不进行分析）"""
        try:
            logger.info(f"尝试连接视频流: {self.rtsp_url}")
            
            # 尝试连接RTSP流
            if self.rtsp_url.startswith('rtsp://'):
                # 优先使用改进版转码器
                try:
                    logger.info("优先尝试通过改进版转码器连接RTSP")
                    self.transcoder = RTSPTranscoder(self.rtsp_url)
                    http_url = self.transcoder.start()
                    
                    if http_url:
                        # 测试能否获取帧
                        time.sleep(1)  # 等待一下，让转码器生成几帧
                        
                        # 检查是否有帧被生成
                        if self.transcoder.get_latest_frame():
                            logger.info(f"转码器连接成功并能获取帧: {http_url}")
                            self.connection_status = "connected_transcoder"
                        else:
                            logger.warning("转码器启动了但未能获取帧，尝试其他方式")
                            self.transcoder.stop()
                            self.transcoder = None
                    else:
                        logger.warning("启动转码器失败")
                        if self.transcoder:
                            self.transcoder.stop()
                            self.transcoder = None
                except Exception as e:
                    logger.warning(f"转码器连接失败: {e}")
                    if self.transcoder:
                        self.transcoder.stop()
                        self.transcoder = None
                
                # 如果转码器方式失败，尝试使用FFmpeg直接读取
                if not self.connection_status.startswith("connected_"):
                    try:
                        logger.info("尝试使用FFmpeg直接读取RTSP流")
                        self.ffmpeg_reader = RTSPStreamReader(self.rtsp_url)
                        success = self.ffmpeg_reader.start()
                        
                        # 测试读取一帧
                        ret, _ = self.ffmpeg_reader.read()
                        if ret:
                            logger.info(f"FFmpeg RTSP流连接成功: {self.rtsp_url}")
                            self.connection_status = "connected_ffmpeg"
                        else:
                            logger.warning("FFmpeg无法读取到帧，尝试其他方式")
                            self.ffmpeg_reader.stop()
                            self.ffmpeg_reader = None
                    except Exception as e:
                        logger.warning(f"FFmpeg处理RTSP失败: {e}，将尝试其他方式")
                        if self.ffmpeg_reader:
                            self.ffmpeg_reader.stop()
                            self.ffmpeg_reader = None
                
                # 如果转码器和FFmpeg都失败，尝试使用OpenCV直接连接
                if not self.connection_status.startswith("connected_"):
                    for attempt in range(3):  # 最多重试3次
                        try:
                            # 配置更加健壮的OpenCV RTSP参数
                            os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = (
                                "rtsp_transport;tcp|"
                                "max_delay;500000|"
                                "stimeout;5000000|"
                                "analyzeduration;2M|"
                                "probesize;2M|"
                                "fflags;discardcorrupt|"
                                "flags;low_delay|"
                                "fpsprobesize;0"
                            )
                            
                            # 构建包含额外参数的URL
                            enhanced_url = self.rtsp_url
                            
                            logger.info(f"使用增强OpenCV参数打开RTSP: {enhanced_url}")
                            self.cap = cv2.VideoCapture(enhanced_url, cv2.CAP_FFMPEG)
                            
                            # 设置更稳健的参数
                            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 5)  # 增加缓冲区
                            self.cap.set(cv2.CAP_PROP_FPS, 15)        # 降低帧率
                            
                            # 测试连接
                            ret, test_frame = self.cap.read()
                            if not ret or test_frame is None:
                                raise Exception("无法读取测试帧")
                                logger.info(f"OpenCV RTSP流连接成功: {enhanced_url}")
                                self.connection_status = "connected_opencv"
                            break
                            
                        except Exception as e:
                            logger.error(f"RTSP连接尝试 {attempt+1}/3 失败: {str(e)}")
                            if attempt == 2:  # 最后一次尝试失败
                                logger.warning(f"所有RTSP流连接方式都失败，使用模拟视频流: {self.rtsp_url}")
                                self.cap = None
                                self.connection_status = "failed_using_mock"
                            time.sleep(1)  # 重试前等待一段时间
                else:
                    self.connection_status = "mock"
                
            self.is_playing = True
            self.thread = threading.Thread(target=self._video_display_loop)
            self.thread.daemon = True
            self.thread.start()
            
            logger.info(f"视频显示已启动: {self.session_id}")
            
        except Exception as e:
            logger.error(f"启动视频显示失败: {e}")
            self.last_error = str(e)
            self.connection_status = "error"
            self.cap = None
            self.is_playing = True
            self.thread = threading.Thread(target=self._video_display_loop)
            self.thread.daemon = True
            self.thread.start()
    
    def start_analysis(self):
        """启动视频分析"""
        if not self.is_playing:
            self.start_video_display()
        
        self.is_running = True
        logger.info(f"视频分析已启动: {self.session_id}")
    
    def stop(self):
        """停止视频流"""
        self.is_running = False
        self.is_playing = False
        
        # 停止线程
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        # 释放资源
        if self.cap:
            self.cap.release()
            self.cap = None
            
        # 停止FFmpeg读取器
        if self.ffmpeg_reader:
            self.ffmpeg_reader.stop()
            self.ffmpeg_reader = None
            
        # 停止本地流服务器
        if self.local_stream:
            self.local_stream.stop()
            self.local_stream = None
        
        # 停止转码器（新增）
        if self.transcoder:
            self.transcoder.stop()
            self.transcoder = None
            
        logger.info(f"视频流 {self.session_id} 停止")
    
    def _video_display_loop(self):
        """视频显示循环"""
        frame_interval = 1.0 / self.fps
        consecutive_errors = 0
        max_consecutive_errors = 5
        last_transcoder_check = 0
        
        while self.is_playing:
            try:
                current_time = time.time()
                
                if current_time - self.last_frame_time >= frame_interval:
                    frame = None
                    
                    # 首先优先从转码器获取帧（如果可用）
                    if self.transcoder is not None and self.transcoder.is_running:
                        try:
                            # 每5秒检查一次转码器状态
                            if current_time - last_transcoder_check >= 5:
                                last_transcoder_check = current_time
                                logger.debug("检查转码器状态...")
                                
                            frame_data = self.transcoder.get_latest_frame()
                            if frame_data:
                                # 将JPEG字节转换为NumPy数组
                                try:
                                    nparr = np.frombuffer(frame_data, np.uint8)
                                    frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                                    if frame is not None:
                                        consecutive_errors = 0
                                        logger.debug("从转码器成功获取帧")
                                except Exception as e:
                                    logger.error(f"转码器帧转换失败: {e}")
                        except Exception as e:
                            logger.error(f"从转码器获取帧失败: {e}")
                            consecutive_errors += 1
                    
                    # 其次尝试从FFmpeg读取
                    if frame is None and self.ffmpeg_reader is not None:
                        try:
                            ret, frame = self.ffmpeg_reader.read()
                            if not ret or frame is None:
                                consecutive_errors += 1
                                logger.warning(f"FFmpeg视频帧读取失败 ({consecutive_errors}/{max_consecutive_errors})")
                                
                                if consecutive_errors >= max_consecutive_errors:
                                    logger.warning("持续FFmpeg帧读取失败，将尝试其他方式")
                                    self.ffmpeg_reader.stop()
                                    self.ffmpeg_reader = None
                                    self.connection_status = "failed_ffmpeg"
                                    consecutive_errors = 0
                                    
                                    # 尝试切换到直接OpenCV方式
                                    try:
                                        logger.info("尝试通过OpenCV直接连接RTSP")
                                        self.cap = cv2.VideoCapture(self.rtsp_url)
                                        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
                                        ret, test_frame = self.cap.read()
                                        if ret and test_frame is not None:
                                            logger.info("OpenCV连接RTSP成功")
                                            self.connection_status = "connected_opencv"
                                        else:
                                            logger.warning("OpenCV连接RTSP失败")
                                            self.cap.release()
                                            self.cap = None
                                            self.connection_status = "failed_using_mock"
                                    except Exception as e:
                                        logger.error(f"OpenCV连接失败: {e}")
                                        self.connection_status = "failed_using_mock"
                            else:
                                consecutive_errors = 0  # 成功读取，重置错误计数
                        except Exception as e:
                            logger.error(f"FFmpeg读取帧时异常: {e}")
                            consecutive_errors += 1
                    
                    # 最后尝试从OpenCV读取
                    if frame is None and self.cap is not None:
                        try:
                            ret, frame = self.cap.read()
                            if not ret or frame is None:
                                consecutive_errors += 1
                                logger.warning(f"OpenCV视频帧读取失败 ({consecutive_errors}/{max_consecutive_errors})")
                                
                                if consecutive_errors >= max_consecutive_errors:
                                    # 简化逻辑：如果连续失败，直接尝试重新连接一次
                                    logger.warning("持续OpenCV帧读取失败，尝试重新连接")
                                    if self.cap:
                                        self.cap.release()
                                        
                                    try:
                                        self.cap = cv2.VideoCapture(self.rtsp_url)
                                        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
                                        ret, test_frame = self.cap.read()
                                        if ret and test_frame is not None:
                                            logger.info("RTSP流重新连接成功")
                                            self.connection_status = "connected_opencv"
                                            consecutive_errors = 0
                                        else:
                                            logger.warning("RTSP流重新连接失败")
                                            self.cap = None
                                            self.connection_status = "failed_using_mock"
                                    except Exception as e:
                                        logger.error(f"RTSP流重新连接失败: {e}")
                                        self.cap = None
                                        self.connection_status = "failed_using_mock"
                            else:
                                consecutive_errors = 0  # 成功读取，重置错误计数
                        except Exception as e:
                            logger.error(f"OpenCV读取帧时异常: {e}")
                            consecutive_errors += 1
                            frame = None
                    
                    # 如果视频流不可用或获取帧失败，生成模拟帧
                    if frame is None:
                        frame = self._generate_mock_frame()
                    
                    if frame is not None:
                        with self.frame_lock:
                            self.current_frame = frame.copy()
                            self.frame_count += 1
                            self.last_frame_time = current_time
                            
                            # 如果正在进行分析，保存帧到缓冲区
                            if self.is_running:
                                self.captured_frames.append((frame.copy(), current_time))
                
                time.sleep(0.01)  # 短暂休眠以降低CPU使用率
                    
            except Exception as e:
                logger.error(f"视频显示循环异常 {self.session_id}: {e}")
                time.sleep(1)
    
    def _generate_mock_frame(self):
        """生成模拟帧"""
        try:
            # 创建一个更真实的模拟视频帧
            frame = np.zeros((720, 1280, 3), dtype=np.uint8)
            
            # 创建动态背景
            t = time.time()
            for i in range(720):
                for j in range(1280):
                    # 创建波浪效果
                    wave1 = np.sin((i + j) * 0.01 + t * 2) * 50
                    wave2 = np.cos((i - j) * 0.008 + t * 1.5) * 30
                    
                    r = int(128 + wave1 + wave2)
                    g = int(100 + np.sin(i * 0.01 + t) * 50)
                    b = int(150 + np.cos(j * 0.01 + t) * 40)
                    
                    frame[i, j] = [
                        max(0, min(255, b)),
                        max(0, min(255, g)), 
                        max(0, min(255, r))
                    ]
            
            # 添加信息文字
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cv2.putText(frame, "RTSP Stream Simulation", 
                       (50, 80), cv2.FONT_HERSHEY_DUPLEX, 1.2, (255, 255, 255), 2)
            cv2.putText(frame, f"Frame: {self.frame_count}", 
                       (50, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(frame, timestamp, 
                       (50, 160), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"Session: {self.session_id[:8]}...", 
                       (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)
            cv2.putText(frame, f"Status: {self.connection_status}", 
                       (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)
            
            # 添加移动元素
            center_x = int(640 + 200 * np.sin(self.frame_count * 0.05))
            center_y = int(360 + 100 * np.cos(self.frame_count * 0.03))
            cv2.circle(frame, (center_x, center_y), 40, (0, 255, 255), -1)
            cv2.circle(frame, (center_x, center_y), 40, (255, 255, 255), 3)
            
            # 添加矩形
            rect_x = int(200 + 100 * np.cos(self.frame_count * 0.02))
            rect_y = int(500)
            cv2.rectangle(frame, (rect_x, rect_y), (rect_x + 80, rect_y + 60), (255, 0, 128), -1)
            
            return frame
            
        except Exception as e:
            logger.error(f"生成模拟帧失败: {e}")
            # 返回纯色帧作为备选
            return np.ones((720, 1280, 3), dtype=np.uint8) * 128
    
    def get_frames_for_analysis(self, mode: str) -> List[np.ndarray]:
        """获取用于分析的帧"""
        frames = []
        
        if mode == "on_demand":
            # 按需采样：获取最近9秒内的帧，每3帧取一帧
            current_time = time.time()
            cutoff_time = current_time - 9  # 9秒前
            
            candidate_frames = [
                frame for frame, timestamp in self.captured_frames 
                if timestamp >= cutoff_time
            ]
            
            # 每3帧取一帧
            frames = candidate_frames[::self.frame_interval]
            
            # 最多取3帧进行质量比较
            if len(frames) > 3:
                frames = frames[-3:]
                
            logger.info(f"按需采样获取了 {len(frames)} 帧用于分析")
            
        elif mode == "real_time":
            # 实时采样：获取最新的一帧
            if self.captured_frames:
                latest_frame = self.captured_frames[-1][0]
                frames = [latest_frame]
                logger.info("实时采样获取最新帧用于分析")
        
        return frames
    
    def get_best_frame_for_analysis(self, mode: str) -> Optional[np.ndarray]:
        """获取最佳帧用于分析"""
        frames = self.get_frames_for_analysis(mode)
        
        if not frames:
            logger.warning("没有可用于分析的帧")
            return None
        
        if mode == "on_demand":
            # 选择质量最好的帧
            return FrameQualityAnalyzer.select_best_frame(frames)
        elif mode == "real_time":
            # 直接返回最新帧
            return frames[0] if frames else None
        
        return None
    
    def get_current_frame(self) -> Optional[np.ndarray]:
        """获取当前显示帧"""
        with self.frame_lock:
            return self.current_frame.copy() if self.current_frame is not None else None
    
    def get_frame_as_bytes(self, frame: Optional[np.ndarray] = None) -> Optional[bytes]:
        """获取帧的字节数据"""
        # 如果当前使用转码器，直接返回最新帧
        if self.transcoder and self.transcoder.is_running:
            latest_frame = self.transcoder.get_latest_frame()
            if latest_frame:
                return latest_frame
        
        # 否则使用常规方式获取帧
        if frame is None:
            frame = self.get_current_frame()
            
        if frame is not None:
            try:
                # 压缩参数优化
                encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 90]
                _, buffer = cv2.imencode('.jpg', frame, encode_param)
                return buffer.tobytes()
            except Exception as e:
                logger.error(f"帧编码失败: {e}")
                return None
        return None
    
    def get_frame_as_base64(self, frame: Optional[np.ndarray] = None) -> Optional[str]:
        """获取帧的base64编码"""
        frame_bytes = self.get_frame_as_bytes(frame)
        if frame_bytes:
            try:
                return base64.b64encode(frame_bytes).decode('utf-8')
            except Exception as e:
                logger.error(f"Base64编码失败: {e}")
                return None
        return None
    
    def get_status(self) -> Dict[str, Any]:
        """获取视频流状态"""
        return {
            "session_id": self.session_id,
            "rtsp_url": self.rtsp_url,
            "is_playing": self.is_playing,
            "is_running": self.is_running,
            "connection_status": self.connection_status,
            "frame_count": self.frame_count,
            "fps": self.fps,
            "has_current_frame": self.current_frame is not None,
            "captured_frames_count": len(self.captured_frames),
            "last_error": self.last_error
        }

class VideoStreamService:
    """视频流服务管理器"""

    def __init__(self):
        self.active_streams: Dict[str, VideoStream] = {}
        self.stream_configs: Dict[str, Dict[str, Any]] = {}
        self.analysis_tasks: Dict[str, asyncio.Task] = {}
        # 音频相关状态
        self.audio_enabled_sessions: Dict[str, bool] = {}
        self.transcription_results: Dict[str, List[Dict[str, Any]]] = {}
        
    async def create_video_session(self, rtsp_url: str, enable_audio: bool = False) -> str:
        """创建视频会话（支持音频）"""
        session_id = str(uuid.uuid4())

        try:
            # 创建视频流
            video_stream = VideoStream(rtsp_url, session_id)
            video_stream.start_video_display()

            # 保存流配置
            self.active_streams[session_id] = video_stream
            self.stream_configs[session_id] = {
                "rtsp_url": rtsp_url,
                "created_at": datetime.now().isoformat(),
                "type": "display_only",
                "audio_enabled": enable_audio
            }

            # 如果启用音频，同时启动音频流
            if enable_audio:
                try:
                    audio_service = get_audio_service()
                    audio_success = await audio_service.start_audio_stream(rtsp_url, session_id)
                    if audio_success:
                        self.audio_enabled_sessions[session_id] = True
                        self.transcription_results[session_id] = []
                        logger.info(f"音频流已启动: {session_id}")
                    else:
                        logger.warning(f"音频流启动失败: {session_id}")
                        self.audio_enabled_sessions[session_id] = False
                except Exception as e:
                    logger.error(f"启动音频流时出错: {e}")
                    self.audio_enabled_sessions[session_id] = False

            # 等待视频流连接完成，最多等待5秒
            max_wait = 5
            wait_count = 0
            while wait_count < max_wait:
                # 检查连接状态
                if video_stream.connection_status.startswith("connected_"):
                    # 再等待一秒以确保至少有一帧可用
                    await asyncio.sleep(1)
                    break

                # 如果连接失败，使用模拟视频流也行
                if video_stream.connection_status == "failed_using_mock":
                    logger.warning(f"使用模拟视频流代替 {rtsp_url}")
                    break

                # 等待一秒再检查
                await asyncio.sleep(1)
                wait_count += 1

            logger.info(f"视频会话 {session_id} 创建成功，连接状态: {video_stream.connection_status}")
            return session_id

        except Exception as e:
            logger.error(f"创建视频会话失败: {e}")
            # 清理资源
            if session_id in self.active_streams:
                self.active_streams[session_id].stop()
                del self.active_streams[session_id]
            # 清理音频资源
            if session_id in self.audio_enabled_sessions:
                try:
                    audio_service = get_audio_service()
                    await audio_service.stop_audio_stream(session_id)
                except:
                    pass
                del self.audio_enabled_sessions[session_id]
            if session_id in self.transcription_results:
                del self.transcription_results[session_id]
            raise
    
    async def start_video_analysis(
        self,
        session_id: str,
        model_id: str,
        prompt: str,
        sampling_mode: str = "on_demand",
        model_manager=None,
        websocket_manager=None
    ) -> bool:
        """开始视频分析"""
        if session_id not in self.active_streams:
            raise ValueError(f"视频会话 {session_id} 不存在")
        
        try:
            video_stream = self.active_streams[session_id]
            video_stream.start_analysis()
            
            # 更新流配置
            self.stream_configs[session_id].update({
                "model_id": model_id,
                "prompt": prompt,
                "sampling_mode": sampling_mode,
                "model_manager": model_manager,
                "websocket_manager": websocket_manager,
                "analysis_started_at": datetime.now().isoformat(),
                "type": "analysis"
            })
            
            # 如果是实时采样模式，启动分析任务
            if sampling_mode == "real_time":
                task = asyncio.create_task(
                    self._real_time_analysis_loop(session_id)
                )
                self.analysis_tasks[session_id] = task
            
            logger.info(f"视频分析已启动: {session_id}, 模式: {sampling_mode}")
            return True
            
        except Exception as e:
            logger.error(f"启动视频分析失败: {e}")
            raise
    
    async def stop_video_analysis(self, session_id: str):
        """停止视频分析（保持视频显示）"""
        if session_id in self.active_streams:
            # 停止分析任务
            if session_id in self.analysis_tasks:
                self.analysis_tasks[session_id].cancel()
                del self.analysis_tasks[session_id]
            
            # 停止分析但保持视频显示
            video_stream = self.active_streams[session_id]
            video_stream.is_running = False
            
            # 更新配置
            if session_id in self.stream_configs:
                self.stream_configs[session_id]["type"] = "display_only"
                self.stream_configs[session_id]["analysis_stopped_at"] = datetime.now().isoformat()
            
            logger.info(f"视频分析已停止，保持视频显示: {session_id}")
        else:
            raise ValueError(f"会话 {session_id} 不存在")
    
    async def start_audio_transcription(self, session_id: str, language: str = "auto", websocket_manager=None) -> bool:
        """开始音频转录"""
        if session_id not in self.active_streams:
            raise ValueError(f"视频会话 {session_id} 不存在")

        if not self.audio_enabled_sessions.get(session_id, False):
            raise ValueError(f"会话 {session_id} 未启用音频")

        try:
            audio_service = get_audio_service()

            # 定义转录回调函数
            def transcription_callback(result: Dict[str, Any]):
                # 保存转录结果
                if session_id in self.transcription_results:
                    self.transcription_results[session_id].append(result)
                    # 只保留最近50条记录
                    if len(self.transcription_results[session_id]) > 50:
                        self.transcription_results[session_id] = self.transcription_results[session_id][-50:]

                # 通过WebSocket发送转录结果
                if websocket_manager and result.get("success", False) and result.get("text", "").strip():
                    asyncio.create_task(websocket_manager.broadcast({
                        "type": "transcription_result",
                        "session_id": session_id,
                        "result": result
                    }))

                logger.info(f"转录结果 [{session_id}]: {result.get('text', '')}")

            # 启动转录
            success = audio_service.start_transcription(session_id, transcription_callback, language)

            if success:
                # 更新配置
                self.stream_configs[session_id]["transcription_active"] = True
                self.stream_configs[session_id]["transcription_language"] = language
                logger.info(f"音频转录已启动: {session_id}")

            return success

        except Exception as e:
            logger.error(f"启动音频转录失败: {e}")
            return False

    async def stop_audio_transcription(self, session_id: str):
        """停止音频转录"""
        if session_id not in self.active_streams:
            raise ValueError(f"视频会话 {session_id} 不存在")

        try:
            audio_service = get_audio_service()
            audio_service.stop_transcription(session_id)

            # 更新配置
            if session_id in self.stream_configs:
                self.stream_configs[session_id]["transcription_active"] = False

            logger.info(f"音频转录已停止: {session_id}")

        except Exception as e:
            logger.error(f"停止音频转录失败: {e}")

    async def stop_video_session(self, session_id: str):
        """完全停止视频会话"""
        if session_id in self.active_streams:
            # 停止分析任务
            if session_id in self.analysis_tasks:
                self.analysis_tasks[session_id].cancel()
                del self.analysis_tasks[session_id]

            # 停止音频流和转录
            if session_id in self.audio_enabled_sessions:
                try:
                    audio_service = get_audio_service()
                    await audio_service.stop_audio_stream(session_id)
                except Exception as e:
                    logger.error(f"停止音频流失败: {e}")
                del self.audio_enabled_sessions[session_id]

            if session_id in self.transcription_results:
                del self.transcription_results[session_id]

            # 停止视频流
            self.active_streams[session_id].stop()
            del self.active_streams[session_id]

            # 清理配置
            if session_id in self.stream_configs:
                del self.stream_configs[session_id]

            logger.info(f"视频会话已完全停止: {session_id}")
        else:
            raise ValueError(f"会话 {session_id} 不存在")
    
    async def analyze_current_frame(self, session_id: str, prompt: str = None) -> Dict[str, Any]:
        """分析当前帧（按需采样模式）"""
        if session_id not in self.active_streams:
            raise ValueError(f"会话 {session_id} 不存在")
        
        config = self.stream_configs[session_id]
        model_manager = config.get("model_manager")
        
        if not model_manager:
            raise ValueError("模型管理器未配置")
        
        # 获取最佳帧进行分析
        video_stream = self.active_streams[session_id]
        sampling_mode = config.get("sampling_mode", "on_demand")
        
        best_frame = video_stream.get_best_frame_for_analysis(sampling_mode)
        
        if best_frame is None:
            return {
                "success": False,
                "error": "无法获取分析帧",
                "session_id": session_id
            }
        
        frame_bytes = video_stream.get_frame_as_bytes(best_frame)
        
        if not frame_bytes:
            return {
                "success": False,
                "error": "帧编码失败",
                "session_id": session_id
            }
        
        # 使用提供的prompt或配置中的prompt
        analysis_prompt = prompt or config["prompt"]
        
        # 调用视频模型分析
        result = await model_manager.analyze_with_video_model(
            config["model_id"],
            frame_bytes,
            analysis_prompt
        )
        
        # 添加会话信息
        result.update({
            "session_id": session_id,
            "frame_count": video_stream.frame_count,
            "timestamp": datetime.now().isoformat(),
            "sampling_mode": sampling_mode
        })
        
        # 发送结果到WebSocket客户端
        websocket_manager = config.get("websocket_manager")
        if websocket_manager:
            await websocket_manager.broadcast({
                "type": "analysis_result",
                "session_id": session_id,
                "result": result
            })
        
        return result

    def get_audio_status(self, session_id: str) -> Dict[str, Any]:
        """获取音频状态"""
        if session_id not in self.active_streams:
            return {"error": "会话不存在"}

        try:
            audio_service = get_audio_service()
            audio_status = audio_service.get_stream_status(session_id)

            return {
                "session_id": session_id,
                "audio_enabled": self.audio_enabled_sessions.get(session_id, False),
                "transcription_active": self.stream_configs.get(session_id, {}).get("transcription_active", False),
                "transcription_language": self.stream_configs.get(session_id, {}).get("transcription_language", "auto"),
                "audio_stream_status": audio_status,
                "transcription_count": len(self.transcription_results.get(session_id, []))
            }

        except Exception as e:
            logger.error(f"获取音频状态失败: {e}")
            return {"error": str(e)}

    def get_transcription_history(self, session_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取转录历史"""
        if session_id not in self.transcription_results:
            return []

        results = self.transcription_results[session_id]
        return results[-limit:] if limit > 0 else results

    async def analyze_multimodal_frame(
        self,
        session_id: str,
        prompt: str = None,
        include_recent_audio: bool = True,
        audio_context_seconds: int = 30
    ) -> Dict[str, Any]:
        """多模态分析当前帧（图像+音频转录+文本提示词）"""
        if session_id not in self.active_streams:
            raise ValueError(f"会话 {session_id} 不存在")

        config = self.stream_configs[session_id]
        model_manager = config.get("model_manager")

        if not model_manager:
            raise ValueError("模型管理器未配置")

        # 获取最佳帧进行分析
        video_stream = self.active_streams[session_id]
        sampling_mode = config.get("sampling_mode", "on_demand")

        best_frame = video_stream.get_best_frame_for_analysis(sampling_mode)

        if best_frame is None:
            return {
                "success": False,
                "error": "无法获取分析帧",
                "session_id": session_id,
                "multimodal": True
            }

        frame_bytes = video_stream.get_frame_as_bytes(best_frame)

        if not frame_bytes:
            return {
                "success": False,
                "error": "帧编码失败",
                "session_id": session_id,
                "multimodal": True
            }

        # 使用提供的prompt或配置中的prompt
        analysis_prompt = prompt or config["prompt"]

        # 获取音频转录内容
        audio_transcription = ""
        if include_recent_audio and session_id in self.transcription_results:
            # 获取最近的音频转录内容
            recent_transcriptions = []
            current_time = datetime.now()

            for transcription in self.transcription_results[session_id]:
                if transcription.get("success", False) and transcription.get("text", "").strip():
                    # 检查时间戳
                    try:
                        transcription_time = datetime.fromisoformat(transcription.get("timestamp", ""))
                        time_diff = (current_time - transcription_time).total_seconds()

                        if time_diff <= audio_context_seconds:
                            recent_transcriptions.append(transcription["text"].strip())
                    except:
                        # 如果时间戳解析失败，包含最近的几条
                        recent_transcriptions.append(transcription["text"].strip())

            # 合并最近的转录内容
            if recent_transcriptions:
                audio_transcription = " ".join(recent_transcriptions[-5:])  # 最多取最近5条

        # 调用多模态分析
        result = await model_manager.analyze_with_multimodal(
            config["model_id"],
            frame_bytes,
            analysis_prompt,
            audio_transcription,
            model_type="video"
        )

        # 添加会话信息
        result.update({
            "session_id": session_id,
            "frame_count": video_stream.frame_count,
            "timestamp": datetime.now().isoformat(),
            "sampling_mode": sampling_mode,
            "audio_context_used": bool(audio_transcription),
            "audio_context_length": len(audio_transcription) if audio_transcription else 0
        })

        # 发送结果到WebSocket客户端
        websocket_manager = config.get("websocket_manager")
        if websocket_manager:
            await websocket_manager.broadcast({
                "type": "multimodal_analysis_result",
                "session_id": session_id,
                "result": result
            })

        return result
    
    async def _real_time_analysis_loop(self, session_id: str):
        """实时分析循环"""
        config = self.stream_configs[session_id]
        
        last_analysis_time = 0
        analysis_interval = 5  # 每5秒分析一次
        
        logger.info(f"启动实时分析循环: {session_id}")
        
        try:
            while session_id in self.active_streams and self.active_streams[session_id].is_running:
                current_time = time.time()
                
                # 控制分析频率
                if current_time - last_analysis_time >= analysis_interval:
                    try:
                        result = await self.analyze_current_frame(session_id)
                        last_analysis_time = current_time
                        
                        logger.info(f"实时分析完成: {session_id}")
                        
                    except Exception as e:
                        logger.error(f"实时分析失败 {session_id}: {e}")
                
                await asyncio.sleep(1)
                
        except asyncio.CancelledError:
            logger.info(f"实时分析循环已取消: {session_id}")
        except Exception as e:
            logger.error(f"实时分析循环异常 {session_id}: {e}")
    
    def get_current_frame(self, session_id: str) -> Optional[str]:
        """获取当前帧的base64编码"""
        if session_id in self.active_streams:
            frame_data = self.active_streams[session_id].get_frame_as_base64()
            if frame_data:
                logger.debug(f"获取帧数据成功: {session_id}, 数据长度: {len(frame_data)}")
            return frame_data
        return None
    
    def get_stream_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取流状态"""
        if session_id in self.active_streams:
            stream_status = self.active_streams[session_id].get_status()
            config = self.stream_configs.get(session_id, {})
            
            # 合并配置和状态信息
            stream_status.update({
                "config": {
                    k: v for k, v in config.items() 
                    if k not in ["model_manager", "websocket_manager"]
                }
            })
            
            return stream_status
        return None
    
    def get_all_streams_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有流状态"""
        return {
            session_id: self.get_stream_status(session_id) 
            for session_id in self.active_streams.keys()
        }

    def _send_frame_to_frontend(self, frame):
        try:
            # 添加错误处理
            if frame is None:
                logger.warning("尝试发送空帧到前端")
                return
                
            # 优化编码参数
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 80]  # 降低质量减少数据量
            success, jpeg_frame = cv2.imencode('.jpg', frame, encode_param)
            
            if not success:
                logger.error("帧编码失败")
                return
                
            jpeg_data = jpeg_frame.tobytes()
            
            # 添加数据大小检查
            if len(jpeg_data) > 1 * 1024 * 1024:  # 超过1MB则警告
                logger.warning(f"帧数据过大: {len(jpeg_data)/1024:.1f}KB")
                
            # 发送帧到前端
            self.websocket_manager.send_message(self.session_id, {
                "type": "video_frame",
                "frameData": base64.b64encode(jpeg_data).decode('utf-8'),
                "timestamp": time.time()  # 添加时间戳
            })
            logger.debug(f"发送视频帧，数据长度: {len(jpeg_data)}")
            
        except Exception as e:
            logger.error(f"发送帧到前端失败: {e}")