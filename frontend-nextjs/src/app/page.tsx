'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { VideoPlayer } from '@/components/VideoPlayer';
import { AudioControls } from '@/components/AudioControls';
import { ModelSelector } from '@/components/ModelSelector';
import { TranscriptionHistory } from '@/components/TranscriptionHistory';
import { AnalysisResults } from '@/components/AnalysisResults';
import { MultimodalAnalysis } from '@/components/MultimodalAnalysis';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import {
  Play,
  Square,
  Settings,
  Wifi,
  WifiOff,
  AlertCircle,
  CheckCircle,
  Volume2
} from 'lucide-react';

import { useAppStore } from '@/store/useAppStore';
import { apiService } from '@/services/api';
import { wsService, isTranscriptionMessage, isMultimodalAnalysisMessage } from '@/services/websocket';
import { generateId, isValidRtspUrl } from '@/lib/utils';
import type { Language, VideoSession, AudioStatus, TranscriptionResult, MultimodalAnalysisResult } from '@/types';

export default function HomePage() {
  // Canvas引用用于视频显示
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 状态管理
  const {
    videoSessions,
    currentSessionId,
    audioStatus,
    transcriptionHistory,
    availableModels,
    selectedModel,
    analysisResults,
    isLoading,
    error,
    wsConnected,
    addVideoSession,
    removeVideoSession,
    setCurrentSession,
    setAudioStatus,
    addTranscriptionResult,
    clearTranscriptionHistory,
    setAvailableModels,
    setSelectedModel,
    addAnalysisResult,
    clearAnalysisResults,
    setLoading,
    setError,
    setWsConnected,
    getCurrentSession,
    getAudioStatus,
    getTranscriptionHistory,
  } = useAppStore();

  // 本地状态
  const [rtspUrl, setRtspUrl] = useState('rtsp://*************:554/12');
  const [audioEnabled, setAudioEnabled] = useState(false);

  const currentSession = getCurrentSession();
  const currentAudioStatus = currentSessionId ? getAudioStatus(currentSessionId) : null;

  // 调试信息
  console.log('当前状态:', {
    currentSessionId,
    currentSession,
    videoSessions,
    hasCurrentSession: !!currentSession
  });
  const currentTranscriptionHistory = currentSessionId ? getTranscriptionHistory(currentSessionId) : [];

  // WebSocket 消息处理
  const handleWebSocketMessage = useCallback((message: any) => {
    if (isTranscriptionMessage(message)) {
      if (message.sessionId) {
        addTranscriptionResult(message.sessionId, message.result);
      }
    } else if (isMultimodalAnalysisMessage(message)) {
      addAnalysisResult(message.result);
    }
  }, [addTranscriptionResult, addAnalysisResult]);

  // 视频帧渲染
  useEffect(() => {
    if (!currentSession || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let animationId: number;

    const drawFrame = () => {
      const time = Date.now() / 1000;

      // 创建动态背景
      const gradient = ctx.createRadialGradient(
        canvas.width / 2, canvas.height / 2, 0,
        canvas.width / 2, canvas.height / 2, Math.max(canvas.width, canvas.height) / 2
      );
      gradient.addColorStop(0, '#1e3a8a');
      gradient.addColorStop(0.5, '#1e40af');
      gradient.addColorStop(1, '#1e293b');

      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 添加动态粒子效果
      ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
      for (let i = 0; i < 20; i++) {
        const x = (Math.sin(time * 0.5 + i) * 0.3 + 0.5) * canvas.width;
        const y = (Math.cos(time * 0.3 + i * 0.5) * 0.3 + 0.5) * canvas.height;
        const size = Math.sin(time * 2 + i) * 2 + 3;

        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();
      }

      // 添加扫描线效果
      const scanLine = (time * 100) % canvas.height;
      ctx.fillStyle = 'rgba(0, 255, 0, 0.1)';
      ctx.fillRect(0, scanLine, canvas.width, 2);

      // 添加中心信息面板
      const panelWidth = 400;
      const panelHeight = 120;
      const panelX = (canvas.width - panelWidth) / 2;
      const panelY = (canvas.height - panelHeight) / 2;

      // 半透明背景
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(panelX, panelY, panelWidth, panelHeight);

      // 边框
      ctx.strokeStyle = 'rgba(34, 197, 94, 0.8)';
      ctx.lineWidth = 2;
      ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);

      // 标题
      ctx.fillStyle = '#22c55e';
      ctx.font = 'bold 20px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        'RTSP 实时视频流',
        canvas.width / 2,
        panelY + 35
      );

      // 信息文本
      ctx.font = '14px Arial';
      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      ctx.fillText(
        `会话ID: ${currentSession.id.slice(0, 8)}...`,
        canvas.width / 2,
        panelY + 60
      );

      ctx.fillText(
        `时间: ${new Date().toLocaleTimeString()}`,
        canvas.width / 2,
        panelY + 80
      );

      // 状态指示器
      const pulse = (Math.sin(time * 3) + 1) / 2;
      ctx.fillStyle = `rgba(34, 197, 94, ${0.5 + pulse * 0.5})`;
      ctx.beginPath();
      ctx.arc(canvas.width / 2 - 80, panelY + 95, 6, 0, Math.PI * 2);
      ctx.fill();

      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      ctx.font = '12px Arial';
      ctx.textAlign = 'left';
      ctx.fillText('● LIVE', canvas.width / 2 - 70, panelY + 100);

      animationId = requestAnimationFrame(drawFrame);
    };

    drawFrame();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [currentSession]);

  // 初始化
  useEffect(() => {
    // 连接 WebSocket
    wsService.connect();
    
    // 添加消息处理器
    const removeMessageHandler = wsService.addMessageHandler(handleWebSocketMessage);
    
    // 添加连接状态处理器
    const removeConnectionHandler = wsService.addConnectionHandler((status) => {
      setWsConnected(status === 'connected');
    });

    // 加载可用模型
    loadModels();

    return () => {
      removeMessageHandler();
      removeConnectionHandler();
      wsService.disconnect();
    };
  }, [handleWebSocketMessage, setWsConnected]);

  // 加载模型列表
  const loadModels = async () => {
    try {
      const models = await apiService.getAvailableModels();

      // 确保 models 是数组
      const modelArray = Array.isArray(models) ? models : [];
      setAvailableModels(modelArray);

      // 自动选择第一个可用的多模态模型
      const multimodalModel = modelArray.find(m => m.type === 'multimodal' && m.isAvailable);
      if (multimodalModel && !selectedModel) {
        setSelectedModel(multimodalModel.id);
      }
    } catch (error) {
      console.error('Failed to load models:', error);
      setError('加载模型列表失败');

      // 设置默认的模型数据以便测试
      setAvailableModels([
        {
          id: 'test-multimodal',
          name: '测试多模态模型',
          type: 'multimodal',
          provider: 'Test',
          description: '用于测试的模拟模型',
          isAvailable: true
        },
        {
          id: 'test-vision',
          name: '测试视觉模型',
          type: 'vision',
          provider: 'Test',
          description: '用于测试的视觉模型',
          isAvailable: true
        }
      ]);
    }
  };

  // 打开视频流
  const handleOpenVideo = async () => {
    if (!isValidRtspUrl(rtspUrl)) {
      setError('请输入有效的RTSP地址');
      return;
    }

    setLoading(true);
    setError(null);
    console.log('开始打开视频流:', rtspUrl);

    try {
      const response = await apiService.openVideo({
        rtsp_url: rtspUrl,
        enable_audio: audioEnabled,
      });

      console.log('API响应:', response);

      if (response.success) {
        const session: VideoSession = {
          id: response.session_id,
          rtspUrl,
          isActive: true,
          audioEnabled,
          createdAt: new Date(),
        };

        console.log('创建会话:', session);
        addVideoSession(session);
        setCurrentSession(response.session_id);

        // 设置音频状态
        if (response.audio_status) {
          setAudioStatus(response.session_id, response.audio_status);
        }

        console.log('视频流打开成功');
      } else {
        console.error('API返回失败:', response);
        setError(response.message || '打开视频流失败');
      }
    } catch (error) {
      console.error('API调用失败:', error);
      setError('连接视频流失败，请检查网络和RTSP地址');

      // 为了测试，我们可以创建一个模拟会话
      console.log('创建模拟会话进行测试');
      const mockSession: VideoSession = {
        id: `mock-${Date.now()}`,
        rtspUrl,
        isActive: true,
        audioEnabled,
        createdAt: new Date(),
      };

      addVideoSession(mockSession);
      setCurrentSession(mockSession.id);
      setError('使用模拟视频流（后端连接失败）');
    } finally {
      setLoading(false);
    }
  };

  // 关闭视频流
  const handleCloseVideo = async () => {
    if (!currentSessionId) return;

    setLoading(true);

    try {
      await apiService.closeVideo(currentSessionId);
      removeVideoSession(currentSessionId);
      setCurrentSession(null);
    } catch (error) {
      console.error('Failed to close video:', error);
      setError('关闭视频流失败');
    } finally {
      setLoading(false);
    }
  };

  // 开始音频转录
  const handleStartTranscription = async (language: Language) => {
    if (!currentSessionId) return;

    try {
      await apiService.startAudioTranscription({
        session_id: currentSessionId,
        language,
      });

      // 更新音频状态
      const currentStatus = getAudioStatus(currentSessionId);
      if (currentStatus) {
        setAudioStatus(currentSessionId, {
          ...currentStatus,
          transcriptionActive: true,
          language,
        });
      }
    } catch (error) {
      setError('启动音频转录失败');
    }
  };

  // 停止音频转录
  const handleStopTranscription = async () => {
    if (!currentSessionId) return;

    try {
      await apiService.stopAudioTranscription(currentSessionId);

      // 更新音频状态
      const currentStatus = getAudioStatus(currentSessionId);
      if (currentStatus) {
        setAudioStatus(currentSessionId, {
          ...currentStatus,
          transcriptionActive: false,
        });
      }
    } catch (error) {
      setError('停止音频转录失败');
    }
  };

  // 多模态分析
  const handleMultimodalAnalysis = async (
    prompt: string,
    includeAudio: boolean,
    contextSeconds: number
  ) => {
    if (!currentSessionId) return;

    setLoading(true);

    try {
      const result = await apiService.analyzeMultimodal({
        session_id: currentSessionId,
        prompt,
        include_recent_audio: includeAudio,
        audio_context_seconds: contextSeconds,
      });

      addAnalysisResult(result);
    } catch (error) {
      setError('多模态分析失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">VA</span>
              </div>
              <h1 className="text-xl font-semibold text-gray-900">
                视频分析系统
              </h1>
            </div>
            
            <div className="flex items-center gap-4">
              {/* WebSocket 状态 */}
              <Badge 
                variant={wsConnected ? "success" : "danger"}
                className="flex items-center gap-1"
              >
                {wsConnected ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                {wsConnected ? '已连接' : '未连接'}
              </Badge>
              
              <Button size="sm" variant="ghost" icon={Settings}>
                设置
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setError(null)}
              className="ml-auto"
            >
              ✕
            </Button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧列 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 视频控制 */}
            <Card>
              <CardHeader>
                <CardTitle>视频流控制</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* 步骤指示 */}
                  {!currentSession && (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          1
                        </div>
                        <h3 className="font-medium text-blue-900">
                          第一步：打开视频流
                        </h3>
                      </div>
                      <p className="text-sm text-blue-700 mt-2 ml-8">
                        输入RTSP地址，选择是否启用音频，然后点击"打开视频流"按钮
                      </p>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="RTSP地址"
                      value={rtspUrl}
                      onChange={(e) => setRtspUrl(e.target.value)}
                      placeholder="rtsp://*************:554/12"
                      disabled={!!currentSession}
                    />

                    <div className="flex items-end gap-2">
                      <label className="flex items-center gap-2 text-sm">
                        <input
                          type="checkbox"
                          checked={audioEnabled}
                          onChange={(e) => setAudioEnabled(e.target.checked)}
                          disabled={!!currentSession}
                          className="rounded"
                        />
                        启用音频处理
                      </label>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {!currentSession ? (
                      <Button
                        onClick={handleOpenVideo}
                        loading={isLoading}
                        icon={Play}
                        className="flex-1"
                      >
                        打开视频流
                      </Button>
                    ) : (
                      <Button
                        onClick={handleCloseVideo}
                        loading={isLoading}
                        variant="danger"
                        icon={Square}
                        className="flex-1"
                      >
                        关闭视频流
                      </Button>
                    )}
                  </div>

                  {/* 视频播放区域 - 直接在按钮下方显示 */}
                  {currentSession && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="mb-4">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">实时视频流</h3>
                        <p className="text-sm text-gray-600">
                          RTSP地址: <span className="font-mono text-xs">{currentSession.rtspUrl}</span>
                        </p>
                      </div>

                      {/* 视频显示区域 */}
                      <div className="relative bg-black rounded-lg overflow-hidden aspect-video border-2 border-gray-300 shadow-lg">
                        <canvas
                          ref={canvasRef}
                          width={1280}
                          height={720}
                          className="w-full h-full object-contain"
                        />

                        {/* 视频状态覆盖层 */}
                        <div className="absolute top-4 left-4">
                          <Badge
                            variant="success"
                            className="bg-green-600 text-white flex items-center gap-1 shadow-md"
                          >
                            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                            实时播放
                          </Badge>
                        </div>

                        {/* 音频状态指示 */}
                        {currentSession.audioEnabled && (
                          <div className="absolute top-4 right-4">
                            <Badge
                              variant="primary"
                              className="bg-blue-600 text-white flex items-center gap-1 shadow-md"
                            >
                              <Volume2 className="w-3 h-3" />
                              音频已启用
                            </Badge>
                          </div>
                        )}

                        {/* 视频控制按钮 */}
                        <div className="absolute bottom-4 right-4 flex gap-2">
                          <Button
                            size="sm"
                            variant="secondary"
                            className="bg-black bg-opacity-50 text-white border-gray-600 hover:bg-opacity-70"
                            onClick={() => {
                              const canvas = canvasRef.current;
                              if (canvas) {
                                const link = document.createElement('a');
                                link.download = `screenshot-${Date.now()}.png`;
                                link.href = canvas.toDataURL();
                                link.click();
                              }
                            }}
                          >
                            截图
                          </Button>
                        </div>

                        {/* 视频信息覆盖层 */}
                        <div className="absolute bottom-4 left-4">
                          <div className="bg-black bg-opacity-70 text-white px-3 py-2 rounded text-sm shadow-md">
                            <div className="flex items-center gap-4">
                              <span className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-green-400 rounded-full" />
                                会话: {currentSession.id.slice(0, 8)}...
                              </span>
                              <span>1280x720</span>
                              <span>{new Date().toLocaleTimeString()}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 多模态分析 - 只在有视频会话时显示 */}
            {currentSession && (
              <div className="space-y-4">
                {/* 步骤指示 */}
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      2
                    </div>
                    <h3 className="font-medium text-blue-900">
                      视频流已打开，现在可以进行智能分析
                    </h3>
                  </div>
                  <p className="text-sm text-blue-700 mt-2 ml-8">
                    请输入分析提示词或选择预设提示词，然后点击"开始分析"按钮
                  </p>
                </div>

                <MultimodalAnalysis
                  sessionId={currentSessionId}
                  audioEnabled={currentSession.audioEnabled || false}
                  onAnalyze={handleMultimodalAnalysis}
                  disabled={false}
                  loading={isLoading}
                />
              </div>
            )}

            {/* 分析结果 - 只在有分析结果时显示 */}
            {analysisResults.length > 0 && (
              <div className="space-y-4">
                {/* 步骤指示 */}
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      3
                    </div>
                    <h3 className="font-medium text-green-900">
                      分析结果
                    </h3>
                  </div>
                  <p className="text-sm text-green-700 mt-2 ml-8">
                    大模型实时输出的分析结果如下
                  </p>
                </div>

                <AnalysisResults
                  results={analysisResults}
                  onClear={clearAnalysisResults}
                />
              </div>
            )}
          </div>

          {/* 右侧列 */}
          <div className="space-y-6">
            {/* 模型选择 */}
            <ModelSelector
              models={availableModels}
              selectedModel={selectedModel}
              onModelSelect={setSelectedModel}
            />

            {/* 音频控制 */}
            {currentSession?.audioEnabled && currentAudioStatus && (
              <AudioControls
                sessionId={currentSession.id}
                audioStatus={currentAudioStatus}
                onStartTranscription={handleStartTranscription}
                onStopTranscription={handleStopTranscription}
                onLanguageChange={() => {}} // 语言变更在开始转录时处理
              />
            )}

            {/* 转录历史 */}
            {currentSession?.audioEnabled && (
              <TranscriptionHistory
                history={currentTranscriptionHistory}
                onClear={() => currentSessionId && clearTranscriptionHistory(currentSessionId)}
              />
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
