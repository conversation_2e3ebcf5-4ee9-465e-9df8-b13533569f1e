'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Play, Settings, Wifi } from 'lucide-react';

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">组件测试页面</h1>
        
        {/* 按钮测试 */}
        <Card>
          <CardHeader>
            <CardTitle>按钮组件测试</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary" icon={Play}>主要按钮</Button>
              <Button variant="secondary">次要按钮</Button>
              <Button variant="success">成功按钮</Button>
              <Button variant="warning">警告按钮</Button>
              <Button variant="danger">危险按钮</Button>
              <Button variant="ghost" icon={Settings}>幽灵按钮</Button>
            </div>
          </CardContent>
        </Card>
        
        {/* 徽章测试 */}
        <Card>
          <CardHeader>
            <CardTitle>徽章组件测试</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Badge variant="primary">主要</Badge>
              <Badge variant="success" className="flex items-center gap-1">
                <Wifi className="w-3 h-3" />
                已连接
              </Badge>
              <Badge variant="warning">警告</Badge>
              <Badge variant="danger">错误</Badge>
              <Badge variant="secondary">次要</Badge>
            </div>
          </CardContent>
        </Card>
        
        {/* 卡片测试 */}
        <Card>
          <CardHeader>
            <CardTitle>卡片组件测试</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              这是一个测试卡片，用于验证组件是否正常工作。
            </p>
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-blue-800">
                ✅ 如果您能看到这个页面，说明 Next.js 项目基本配置正确！
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
