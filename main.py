from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, validator
import asyncio
import cv2
import base64
import json
import logging
from typing import Optional, Dict, Any
import threading
import time
from datetime import datetime
import os
from pathlib import Path
import re
import subprocess

# 导入自定义模块
from models.model_manager import ModelManager
from services.video_service import VideoStreamService, RTSPTranscoder
from services.image_service import ImageAnalysisService
from services.audio_service import audio_service
from utils.websocket_manager import WebSocketManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="视频图像分析系统", version="2.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
os.makedirs("uploads", exist_ok=True)
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# 全局服务实例
model_manager = ModelManager()
video_service = VideoStreamService()
image_service = ImageAnalysisService()
websocket_manager = WebSocketManager()

# 数据模型
class VideoOpenRequest(BaseModel):
    rtsp_url: str
    enable_audio: bool = False

    @validator('rtsp_url')
    def validate_rtsp_url(cls, v):
        """验证RTSP URL的格式"""
        if not v:
            raise ValueError("RTSP URL不能为空")

        # 验证RTSP URL格式
        if not v.startswith('rtsp://'):
            raise ValueError("无效的RTSP URL，必须以'rtsp://'开头")

        # 基本格式验证
        rtsp_pattern = r'^rtsp://[a-zA-Z0-9\.\-_]+(\:[0-9]+)?(/[a-zA-Z0-9\-_\.~!$&\'()*+,;=:@/%]+)?$'
        if not re.match(rtsp_pattern, v):
            raise ValueError("RTSP URL格式无效")

        return v

class VideoAnalysisStartRequest(BaseModel):
    session_id: str
    model_id: str
    prompt: str
    sampling_mode: str = "on_demand"

class VideoAnalysisStopRequest(BaseModel):
    session_id: str

class VideoFrameAnalysisRequest(BaseModel):
    session_id: str
    prompt: str

class VideoAnalysisRequest(BaseModel):
    rtsp_url: str
    model_name: str
    prompt: str
    sampling_mode: str = "on_demand"

class ImageAnalysisRequest(BaseModel):
    model_name: str
    prompt: str

# 音频相关请求模型
class AudioOpenRequest(BaseModel):
    rtsp_url: str

    @validator('rtsp_url')
    def validate_rtsp_url(cls, v):
        """验证RTSP URL的格式"""
        if not v:
            raise ValueError("RTSP URL不能为空")

        # 验证RTSP URL格式
        if not v.startswith('rtsp://'):
            raise ValueError("无效的RTSP URL，必须以'rtsp://'开头")

        # 基本格式验证
        rtsp_pattern = r'^rtsp://[a-zA-Z0-9\.\-_]+(\:[0-9]+)?(/[a-zA-Z0-9\-_\.~!$&\'()*+,;=:@/%]+)?$'
        if not re.match(rtsp_pattern, v):
            raise ValueError("RTSP URL格式无效")

        return v

class AudioTranscriptionRequest(BaseModel):
    session_id: str
    language: str = "auto"

class MultimodalAnalysisRequest(BaseModel):
    session_id: str
    prompt: str
    include_recent_audio: bool = True
    audio_context_seconds: int = 30

class ModelConfig(BaseModel):
    name: str
    type: str
    api_endpoint: str
    api_key: Optional[str] = None
    config: Dict[str, Any] = {}

class TranscoderStartRequest(BaseModel):
    rtsp_url: str
    port: Optional[int] = None
    
    @validator('rtsp_url')
    def validate_rtsp_url(cls, v):
        """验证RTSP URL的格式"""
        if not v:
            raise ValueError("RTSP URL不能为空")
        
        # 验证RTSP URL格式
        if not v.startswith('rtsp://'):
            raise ValueError("无效的RTSP URL，必须以'rtsp://'开头")
        
        # 基本格式验证
        rtsp_pattern = r'^rtsp://[a-zA-Z0-9\.\-_]+(\:[0-9]+)?(/[a-zA-Z0-9\-_\.~!$&\'()*+,;=:@/%]+)?$'
        if not re.match(rtsp_pattern, v):
            raise ValueError("RTSP URL格式无效")
            
        return v

# 用于存储已启动的转码器
active_transcoders = {}

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化服务"""
    logger.info("启动视频图像分析系统...")

    # 检查FFmpeg是否可用
    try:
        result = subprocess.run(
            ["ffmpeg", "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False
        )
        if result.returncode == 0:
            ffmpeg_version = result.stdout.split('\n')[0]
            logger.info(f"FFmpeg可用: {ffmpeg_version}")
        else:
            logger.warning("FFmpeg不可用，一些视频处理功能将受限")
    except Exception as e:
        logger.warning(f"检查FFmpeg失败: {e}")

    # 加载模型配置
    await model_manager.load_model_configs()

    # 初始化音频服务
    await audio_service.initialize()

    logger.info("系统启动完成")

@app.get("/")
async def root():
    return {"message": "视频图像分析系统API", "version": "2.0.0"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "active_streams": len(video_service.active_streams),
        "available_video_models": len(model_manager.get_video_models()),
        "available_image_models": len(model_manager.get_image_models())
    }

# 模型相关接口
@app.get("/models")
async def get_available_models():
    """获取可用的模型列表（兼容旧版）"""
    video_models = model_manager.get_video_models()
    image_models = model_manager.get_image_models()
    
    # 合并所有模型，返回模型名称列表
    all_models = []
    for model in video_models:
        all_models.append(model["name"])
    for model in image_models:
        if model["name"] not in all_models:
            all_models.append(model["name"])
    
    return {"models": all_models}

@app.get("/models/video")
async def get_video_models():
    """获取视频模型列表"""
    return {"models": model_manager.get_video_models()}

@app.get("/models/image")
async def get_image_models():
    """获取图片模型列表"""
    return {"models": model_manager.get_image_models()}

@app.post("/models")
async def add_model(model_config: ModelConfig):
    """添加新的模型配置"""
    try:
        await model_manager.add_model(model_config.dict())
        return {"message": "模型添加成功", "model": model_config.name}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# 新版视频接口
@app.post("/video/open")
async def open_video(request: VideoOpenRequest):
    """打开视频流（支持音频）"""
    try:
        logger.info(f"尝试打开RTSP视频流: {request.rtsp_url}, 音频: {request.enable_audio}")

        # 使用视频服务创建会话
        session_id = await video_service.create_video_session(request.rtsp_url, request.enable_audio)

        # 等待一小段时间，让视频连接初始化
        await asyncio.sleep(1)

        # 检查视频流状态
        status = video_service.get_stream_status(session_id)
        audio_status = video_service.get_audio_status(session_id) if request.enable_audio else None

        if status and status.get("connection_status", "").startswith("failed_"):
            logger.warning(f"视频流连接状态异常: {status['connection_status']}")

            # 如果是模拟模式，通知前端
            if status["connection_status"] == "failed_using_mock":
                return {
                    "success": True,
                    "message": "RTSP流连接失败，使用模拟视频",
                    "session_id": session_id,
                    "rtsp_url": request.rtsp_url,
                    "audio_enabled": request.enable_audio,
                    "audio_status": audio_status,
                    "warning": "无法连接到实际RTSP流，使用模拟视频替代"
                }

        return {
            "success": True,
            "message": "视频已打开",
            "session_id": session_id,
            "rtsp_url": request.rtsp_url,
            "audio_enabled": request.enable_audio,
            "audio_status": audio_status,
            "connection_status": status.get("connection_status") if status else "unknown"
        }
    except Exception as e:
        logger.error(f"打开视频失败: {e}")
        return {
            "success": False,
            "message": f"打开视频失败: {str(e)}",
            "session_id": "",
            "audio_status": None
        }

@app.post("/video/close/{session_id}")
async def close_video(session_id: str):
    """关闭视频流"""
    try:
        await video_service.stop_video_session(session_id)
        return {"message": "视频已关闭", "session_id": session_id}
    except Exception as e:
        logger.error(f"关闭视频失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/video/analyze/start")
async def start_video_analysis(request: VideoAnalysisStartRequest):
    """开始视频分析"""
    try:
        success = await video_service.start_video_analysis(
            session_id=request.session_id,
            model_id=request.model_id,
            prompt=request.prompt,
            sampling_mode=request.sampling_mode,
            model_manager=model_manager,
            websocket_manager=websocket_manager
        )
        return {
            "message": "视频分析已开始",
            "session_id": request.session_id,
            "success": success
        }
    except Exception as e:
        logger.error(f"启动视频分析失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/video/analyze/stop")
async def stop_video_analysis(request: VideoAnalysisStopRequest):
    """停止视频分析"""
    try:
        await video_service.stop_video_analysis(request.session_id)
        return {"message": "视频分析已停止", "session_id": request.session_id}
    except Exception as e:
        logger.error(f"停止视频分析失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/video/analyze/frame")
async def analyze_video_frame(request: VideoFrameAnalysisRequest):
    """分析视频帧"""
    try:
        result = await video_service.analyze_current_frame(
            session_id=request.session_id,
            prompt=request.prompt
        )
        return {"result": result}
    except Exception as e:
        logger.error(f"分析视频帧失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# 音频相关接口
@app.post("/audio/transcription/start")
async def start_audio_transcription(request: AudioTranscriptionRequest):
    """开始音频转录"""
    try:
        success = await video_service.start_audio_transcription(
            session_id=request.session_id,
            language=request.language,
            websocket_manager=websocket_manager
        )
        return {
            "message": "音频转录已开始" if success else "音频转录启动失败",
            "session_id": request.session_id,
            "success": success,
            "language": request.language
        }
    except Exception as e:
        logger.error(f"启动音频转录失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/audio/transcription/stop/{session_id}")
async def stop_audio_transcription(session_id: str):
    """停止音频转录"""
    try:
        await video_service.stop_audio_transcription(session_id)
        return {"message": "音频转录已停止", "session_id": session_id}
    except Exception as e:
        logger.error(f"停止音频转录失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/audio/status/{session_id}")
async def get_audio_status(session_id: str):
    """获取音频状态"""
    try:
        status = video_service.get_audio_status(session_id)
        return {"status": status}
    except Exception as e:
        logger.error(f"获取音频状态失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/audio/transcription/history/{session_id}")
async def get_transcription_history(session_id: str, limit: int = 10):
    """获取转录历史"""
    try:
        history = video_service.get_transcription_history(session_id, limit)
        return {"history": history, "session_id": session_id}
    except Exception as e:
        logger.error(f"获取转录历史失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# 多模态分析接口
@app.post("/multimodal/analyze")
async def analyze_multimodal(request: MultimodalAnalysisRequest):
    """多模态分析（视频+音频+文本）"""
    try:
        result = await video_service.analyze_multimodal_frame(
            session_id=request.session_id,
            prompt=request.prompt,
            include_recent_audio=request.include_recent_audio,
            audio_context_seconds=request.audio_context_seconds
        )
        return {"result": result}
    except Exception as e:
        logger.error(f"多模态分析失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# 兼容旧版视频接口
@app.post("/video/start")
async def start_video_analysis_legacy(request: VideoAnalysisRequest):
    """开始视频分析（兼容旧版）"""
    try:
        # 先创建视频会话
        session_id = await video_service.create_video_session(request.rtsp_url)
        
        # 找到对应的模型ID
        model_id = request.model_name
        video_models = model_manager.get_video_models()
        for model in video_models:
            if model["name"] == request.model_name:
                model_id = model["id"]
                break
        
        # 开始分析
        await video_service.start_video_analysis(
            session_id=session_id,
            model_id=model_id,
            prompt=request.prompt,
            sampling_mode=request.sampling_mode,
            model_manager=model_manager,
            websocket_manager=websocket_manager
        )
        
        return {"message": "视频分析已开始", "session_id": session_id}
    except Exception as e:
        logger.error(f"启动视频分析失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/video/stop/{session_id}")
async def stop_video_analysis_legacy(session_id: str):
    """停止视频分析（兼容旧版）"""
    try:
        await video_service.stop_video_session(session_id)
        return {"message": "视频分析已停止", "session_id": session_id}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/video/analyze/{session_id}")
async def analyze_current_frame_legacy(session_id: str, request: dict):
    """分析当前帧（兼容旧版）"""
    try:
        prompt = request.get("prompt", "请描述这张图片")
        result = await video_service.analyze_current_frame(session_id, prompt)
        return {"result": result}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# 图片分析接口
@app.post("/image/analyze")
async def analyze_image(
    file: UploadFile = File(...),
    model_id: str = None,
    model_name: str = None,
    prompt: str = "请分析这张图片"
):
    """分析上传的图片"""
    try:
        # 保存上传的文件
        file_path = f"uploads/{file.filename}"
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 确定使用的模型ID
        final_model_id = model_id
        if not final_model_id and model_name:
            # 通过模型名称查找模型ID
            image_models = model_manager.get_image_models()
            for model in image_models:
                if model["name"] == model_name:
                    final_model_id = model["id"]
                    break
            
            # 如果还没找到，使用模型名称作为ID
            if not final_model_id:
                final_model_id = model_name
        
        # 如果都没有，使用默认模型
        if not final_model_id:
            image_models = model_manager.get_image_models()
            if image_models:
                final_model_id = image_models[0]["id"]
            else:
                final_model_id = "mock-image-model"
        
        # 分析图片
        result = await image_service.analyze_image(
            image_path=file_path,
            model_name=final_model_id,
            prompt=prompt,
            model_manager=model_manager
        )
        
        return {
            "result": result,
            "file_path": file_path,
            "analysis_time": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"图片分析失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# WebSocket接口
@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket连接处理"""
    await websocket_manager.connect(websocket, client_id)
    logger.info(f"客户端 {client_id} 已连接")
    
    try:
        while True:
            # 接收消息
            message = await websocket.receive_json()
            logger.debug(f"收到消息: {message}")
            
            # 处理消息
            if message.get("type") == "ping":
                # 心跳检测
                await websocket_manager.send_message(client_id, {
                    "type": "pong",
                    "timestamp": datetime.now().timestamp()
                })
            elif message.get("type") == "request_frame":
                # 请求当前帧
                session_id = message.get("session_id")
                if session_id:
                    try:
                        logger.info(f"收到帧请求，会话ID: {session_id}")

                        # 检查会话是否存在
                        stream_status = video_service.get_stream_status(session_id)
                        if not stream_status:
                            logger.warning(f"会话不存在: {session_id}")
                            await websocket_manager.send_message(client_id, {
                                "type": "error",
                                "error": f"视频会话 {session_id} 不存在",
                                "session_id": session_id
                            })
                            continue

                        logger.info(f"会话状态: {stream_status}")

                        # 获取帧数据
                        frame_data = video_service.get_current_frame(session_id)
                        if frame_data:
                                # 发送帧数据
                                await websocket_manager.send_message(client_id, {
                                    "type": "video_frame",
                                    "frameData": frame_data,
                                    "session_id": session_id,
                                    "timestamp": datetime.now().timestamp()
                                })
                                logger.info(f"帧数据已发送，长度: {len(frame_data) if frame_data else 0}")
                        else:
                            logger.warning(f"无法获取帧数据，会话: {session_id}")
                            # 如果没有帧数据，发送错误消息
                            await websocket_manager.send_message(client_id, {
                                "type": "error",
                                "error": "无法获取视频帧，请检查视频流连接",
                                "session_id": session_id
                            })
                    except Exception as e:
                        logger.error(f"处理帧请求失败: {e}")
                        await websocket_manager.send_message(client_id, {
                            "type": "error",
                            "error": f"获取帧失败: {str(e)}",
                            "session_id": session_id
                        })
                        
    except WebSocketDisconnect:
        websocket_manager.disconnect(client_id)
        logger.info(f"客户端 {client_id} 断开连接")
    except Exception as e:
        logger.error(f"WebSocket处理异常: {e}")
        try:
            websocket_manager.disconnect(client_id)
        except:
            pass

# 状态查询接口
@app.get("/video/status/{session_id}")
async def get_video_status(session_id: str):
    """获取视频流状态"""
    try:
        status = video_service.get_stream_status(session_id)
        if status:
            return status
        else:
            raise HTTPException(status_code=404, detail="会话不存在")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/video/status")
async def get_all_video_status():
    """获取所有视频流状态"""
    return video_service.get_all_streams_status()

@app.get("/video/frame/{session_id}")
async def get_video_frame(session_id: str):
    """获取视频会话的当前帧"""
    try:
        frame_data = video_service.get_current_frame(session_id)
        if frame_data:
            return {"frame": frame_data}
        else:
            raise HTTPException(status_code=404, detail="未能获取当前帧，可能是会话无效或帧未就绪")
    except Exception as e:
        logger.error(f"获取视频帧失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# 故障诊断API - RTSP流检测和修复
@app.post("/tools/check-rtsp")
async def check_rtsp_url(request: VideoOpenRequest):
    """检查RTSP流并返回详细诊断结果"""
    try:
        result = {
            "rtsp_url": request.rtsp_url,
            "diagnostic": {},
            "status": "checking",
            "suggestions": []
        }
        
        # 1. 检查URL格式和可连接性
        if not request.rtsp_url.startswith("rtsp://"):
            result["status"] = "error"
            result["diagnostic"]["url_format"] = "不是有效的RTSP URL"
            return result
        
        # 2. 使用FFmpeg诊断流
        try:
            cmd = [
                "ffprobe",
                "-v", "error",
                "-show_entries", "stream=width,height,codec_name,codec_type",
                "-of", "json",
                "-rtsp_transport", "tcp",
                "-i", request.rtsp_url
            ]
            
            logger.info(f"RTSP诊断: {' '.join(cmd)}")
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                try:
                    info = json.loads(stdout)
                    result["diagnostic"]["stream_info"] = info
                    result["status"] = "available"
                    
                    # 检查编解码器
                    streams = info.get("streams", [])
                    video_streams = [s for s in streams if s.get("codec_type") == "video"]
                    
                    if video_streams:
                        codec = video_streams[0].get("codec_name", "unknown")
                        result["diagnostic"]["video_codec"] = codec
                        
                        # H.264特殊处理建议
                        if codec == "h264":
                            result["suggestions"].append(
                                "此流使用H.264编码，如遇解码问题，请尝试使用FFmpeg直接读取或降低视频分辨率。"
                            )
                    
                except json.JSONDecodeError:
                    result["diagnostic"]["ffprobe_output"] = stdout.decode("utf-8")
            else:
                result["status"] = "error"
                result["diagnostic"]["ffprobe_error"] = stderr.decode("utf-8")
                result["suggestions"].append("RTSP流访问失败，请检查URL是否正确，以及提供的账号密码是否正确。")
                
        except Exception as e:
            result["status"] = "error"
            result["diagnostic"]["probe_error"] = str(e)
            
        # 3. 尝试使用OpenCV连接
        try:
            cap = cv2.VideoCapture(request.rtsp_url)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    result["diagnostic"]["opencv_connection"] = "success"
                    # 保存采样帧用于分析
                    frame_path = f"uploads/rtsp_test_{int(time.time())}.jpg"
                    cv2.imwrite(frame_path, frame)
                    result["diagnostic"]["sample_frame"] = f"/{frame_path}"
                else:
                    result["diagnostic"]["opencv_connection"] = "opened_but_no_frames"
            else:
                result["diagnostic"]["opencv_connection"] = "failed"
                
            cap.release()
        except Exception as e:
            result["diagnostic"]["opencv_error"] = str(e)
            
        # 4. 如果出现问题，添加修复建议
        if result["status"] == "error":
            result["suggestions"].extend([
                "尝试降低流的分辨率或帧率",
                "检查网络连接是否稳定",
                "确保RTSP服务器允许TCP连接",
                "尝试使用FFmpeg进行流转码后访问"
            ])
            
        return result
        
    except Exception as e:
        logger.error(f"检查RTSP流失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# 转码器相关接口
@app.post("/rtsp/transcode")
async def start_transcoder(request: TranscoderStartRequest):
    """启动RTSP转码器，将RTSP流转换为HTTP MJPEG流"""
    try:
        # 检查是否已有相同URL的转码器在运行
        for url, transcoder in active_transcoders.items():
            if url == request.rtsp_url and transcoder.is_running:
                logger.info(f"该RTSP URL已有转码器运行: {request.rtsp_url}")
                return {
                    "message": "转码器已在运行",
                    "rtsp_url": request.rtsp_url,
                    "http_url": transcoder.http_url,
                    "port": transcoder.http_port
                }
        
        # 创建并启动新的转码器
        transcoder = RTSPTranscoder(request.rtsp_url, request.port)
        http_url = transcoder.start()
        
        if http_url:
            # 存储转码器实例
            active_transcoders[request.rtsp_url] = transcoder
            
            logger.info(f"RTSP转码器已启动: {request.rtsp_url} -> {http_url}")
            return {
                "message": "转码器已启动",
                "rtsp_url": request.rtsp_url,
                "http_url": http_url,
                "port": transcoder.http_port
            }
        else:
            raise HTTPException(status_code=500, detail="转码器启动失败")
    except Exception as e:
        logger.error(f"启动转码器失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/rtsp/transcode/stop")
async def stop_transcoder(request: TranscoderStartRequest):
    """停止RTSP转码器"""
    try:
        if request.rtsp_url in active_transcoders:
            transcoder = active_transcoders[request.rtsp_url]
            transcoder.stop()
            del active_transcoders[request.rtsp_url]
            return {"message": "转码器已停止", "rtsp_url": request.rtsp_url}
        else:
            return {"message": "未找到指定URL的转码器", "rtsp_url": request.rtsp_url}
    except Exception as e:
        logger.error(f"停止转码器失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/rtsp/transcode/list")
async def list_transcoders():
    """列出所有活动的转码器"""
    result = []
    for url, transcoder in active_transcoders.items():
        if transcoder.is_running:
            result.append({
                "rtsp_url": url,
                "http_url": transcoder.http_url,
                "port": transcoder.http_port
            })
    return {"transcoders": result}

@app.post("/rtsp/check")
async def check_rtsp_url(request: TranscoderStartRequest):
    """检查RTSP URL是否可访问"""
    try:
        logger.info(f"检查RTSP URL: {request.rtsp_url}")
        
        # 使用FFmpeg检查RTSP流
        cmd = [
            "ffprobe",
            "-v", "error",
            "-rtsp_transport", "tcp",
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height,codec_name",
            "-of", "json",
            request.rtsp_url
        ]
        
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=10  # 10秒超时
        )
        
        if result.returncode == 0:
            # 尝试解析结果
            try:
                output = json.loads(result.stdout)
                streams = output.get("streams", [])
                if streams:
                    stream_info = streams[0]
                    return {
                        "status": "accessible",
                        "rtsp_url": request.rtsp_url,
                        "stream_info": {
                            "width": stream_info.get("width"),
                            "height": stream_info.get("height"),
                            "codec": stream_info.get("codec_name")
                        },
                        "message": "RTSP流可访问"
                    }
            except json.JSONDecodeError:
                pass
                
            # 如果无法解析但返回码为0，仍认为是可访问的
            return {
                "status": "accessible",
                "rtsp_url": request.rtsp_url,
                "message": "RTSP流可访问，但无法获取详细信息"
            }
        else:
            return {
                "status": "inaccessible",
                "rtsp_url": request.rtsp_url,
                "error": result.stderr,
                "message": "RTSP流不可访问"
            }
    except subprocess.TimeoutExpired:
        return {
            "status": "timeout",
            "rtsp_url": request.rtsp_url,
            "message": "检查RTSP流超时"
        }
    except Exception as e:
        logger.error(f"检查RTSP URL失败: {e}")
        return {
            "status": "error",
            "rtsp_url": request.rtsp_url,
            "error": str(e),
            "message": "检查过程发生错误"
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )