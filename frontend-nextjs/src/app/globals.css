@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center gap-2 rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }

  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
  }

  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 active:bg-warning-800;
  }

  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;
  }

  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 active:bg-gray-200;
  }

  /* Button sizes */
  .btn-sm {
    @apply h-8 px-3 text-sm;
  }

  .btn-md {
    @apply h-10 px-4 text-sm;
  }

  .btn-lg {
    @apply h-12 px-6 text-base;
  }

  /* Card components */
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm;
  }

  .card-header {
    @apply p-6 pb-4;
  }

  .card-title {
    @apply text-lg font-semibold text-gray-900;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  /* Badge variants */
  .badge {
    @apply inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }

  /* Loading spinner */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-current border-t-transparent;
  }

  /* Input styles */
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }

  .input-error {
    @apply border-danger-300 focus:ring-danger-500 focus:border-danger-500;
  }

  /* Select styles */
  .select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white;
  }
}

@layer base {
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 h-10 py-2 px-4;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 h-10 py-2 px-4;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 h-10 py-2 px-4;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 h-10 py-2 px-4;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 h-10 py-2 px-4;
  }
  
  .btn-sm {
    @apply btn h-8 px-3 text-xs;
  }
  
  .btn-lg {
    @apply btn h-12 px-8;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .select {
    @apply input cursor-pointer;
  }
  
  .textarea {
    @apply input min-h-[80px] resize-none;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white p-6 shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 pb-4;
  }
  
  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }
  
  .card-content {
    @apply pt-0;
  }
  
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
