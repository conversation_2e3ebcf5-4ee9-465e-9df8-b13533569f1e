// 基础类型定义
export interface VideoSession {
  id: string;
  rtspUrl: string;
  isActive: boolean;
  audioEnabled: boolean;
  createdAt: Date;
  currentFrame?: string; // base64编码的视频帧数据
}

export interface AudioStatus {
  enabled: boolean;
  transcriptionActive: boolean;
  language: string;
  status: 'idle' | 'processing' | 'error';
}

export interface TranscriptionResult {
  id: string;
  text: string;
  language: string;
  confidence: number;
  timestamp: Date;
  duration?: number;
}

export interface MultimodalAnalysisResult {
  id: string;
  sessionId: string;
  prompt: string;
  visualAnalysis: string;
  audioContext?: string;
  combinedAnalysis: string;
  timestamp: Date;
  processingTime: number;
}

export interface ModelInfo {
  id: string;
  name: string;
  type: 'vision' | 'language' | 'multimodal';
  provider: string;
  description?: string;
  isAvailable: boolean;
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: 'frame_analysis' | 'transcription_result' | 'multimodal_analysis_result' | 'video_frame' | 'error' | 'status';
  sessionId?: string;
  data?: any;
  timestamp: Date;
}

export interface FrameAnalysisMessage extends WebSocketMessage {
  type: 'frame_analysis';
  result: {
    text: string;
    confidence: number;
    processingTime: number;
  };
}

export interface TranscriptionMessage extends WebSocketMessage {
  type: 'transcription_result';
  result: TranscriptionResult;
}

export interface MultimodalAnalysisMessage extends WebSocketMessage {
  type: 'multimodal_analysis_result';
  result: MultimodalAnalysisResult;
}

export interface VideoFrameMessage extends WebSocketMessage {
  type: 'video_frame';
  frameData: string;
  session_id: string;
  timestamp?: number;
}

// API 请求/响应类型
export interface VideoOpenRequest {
  rtsp_url: string;
  enable_audio?: boolean;
}

export interface VideoOpenResponse {
  success: boolean;
  session_id: string;
  message?: string;
  audio_status?: AudioStatus;
}

export interface AudioTranscriptionRequest {
  session_id: string;
  language: string;
}

export interface MultimodalAnalysisRequest {
  session_id: string;
  prompt: string;
  include_recent_audio?: boolean;
  audio_context_seconds?: number;
}

// 应用状态类型
export interface AppState {
  // 视频状态
  videoSessions: Record<string, VideoSession>;
  currentSessionId: string | null;
  
  // 音频状态
  audioStatus: Record<string, AudioStatus>;
  transcriptionHistory: Record<string, TranscriptionResult[]>;
  
  // 模型状态
  availableModels: ModelInfo[];
  selectedModel: string | null;
  
  // 分析结果
  analysisResults: MultimodalAnalysisResult[];
  
  // UI 状态
  isLoading: boolean;
  error: string | null;
  
  // WebSocket 状态
  wsConnected: boolean;
  wsReconnecting: boolean;
}

// 组件 Props 类型
export interface VideoPlayerProps {
  sessionId: string;
  rtspUrl: string;
  onError?: (error: string) => void;
}

export interface AudioControlsProps {
  sessionId: string;
  audioStatus: AudioStatus;
  onStartTranscription: (language: string) => void;
  onStopTranscription: () => void;
  onLanguageChange: (language: string) => void;
}

export interface ModelSelectorProps {
  models: ModelInfo[];
  selectedModel: string | null;
  onModelSelect: (modelId: string) => void;
  disabled?: boolean;
}

export interface AnalysisResultsProps {
  results: MultimodalAnalysisResult[];
  onClear?: () => void;
  maxResults?: number;
}

export interface TranscriptionHistoryProps {
  history: TranscriptionResult[];
  onClear?: () => void;
  maxItems?: number;
}

// 工具类型
export type Language = 'auto' | 'zh' | 'en' | 'yue' | 'ja' | 'ko' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'ru' | 'ar' | 'hi' | 'th' | 'vi';

export interface LanguageOption {
  value: Language;
  label: string;
  flag?: string;
}

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// 配置类型
export interface AppConfig {
  apiBaseUrl: string;
  wsUrl: string;
  maxTranscriptionHistory: number;
  maxAnalysisResults: number;
  audioContextSeconds: number;
  reconnectInterval: number;
  maxReconnectAttempts: number;
}
