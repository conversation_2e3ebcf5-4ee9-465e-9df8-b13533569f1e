import React, { useState, useEffect, useRef } from 'react';
import './App.css';

// 配置
const API_BASE_URL = 'http://localhost:8000';
const WS_BASE_URL = 'ws://localhost:8000';

function App() {
  // 主要状态管理
  const [activeTab, setActiveTab] = useState('video'); // 'video' or 'image'
  const [videoModels, setVideoModels] = useState([]);
  const [imageModels, setImageModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [systemStatus, setSystemStatus] = useState('');
  const [debugLog, setDebugLog] = useState([]);
  const [analysisResults, setAnalysisResults] = useState([]);
  // 视频分析相关状态
  const videoPlayerRef = useRef(null);

  // 本地大模型配置
  const [localLLMConfig, setLocalLLMConfig] = useState({
    enabled: false,                   // 是否启用本地LLM
    apiUrl: 'http://**************:8080',  // llama.cpp服务地址
    maxTokens: 200,                   // 最大生成的token数
    analysisInterval: 2000,           // 分析间隔(ms)
    isProcessing: false,              // 是否正在处理
  });

  // 本地分析定时器
  const localAnalysisIntervalRef = useRef(null);

  const [videoState, setVideoState] = useState({
    streamUrl: 'rtsp://192.168.1.109:554/12',
    useTranscoder: false,         // 是否使用转码器
    transcoderUrl: '',            // 转码器URL
    mjpegUrl: '',                 // 用于直接显示MJPEG流的URL
    currentFrame: null,
    samplingMode: 'on_demand',    // 'on_demand' or 'real_time'
    selectedModel: '',
    prompt: '请详细描述视频中发生的情况，包括场景、人物、动作和重要细节。',
    currentSession: null,
    isStreaming: false,
    isVideoPlaying: false,
    directStreamEnabled: false,   // 是否启用直接流显示
    showDirectStream: true,       // 默认显示直接流
    // 音频相关状态
    audioEnabled: false,          // 是否启用音频
    audioStatus: null,            // 音频状态
    transcriptionActive: false,   // 转录是否激活
    transcriptionLanguage: 'auto', // 转录语言
    transcriptionHistory: [],     // 转录历史
    multimodalMode: false,        // 多模态分析模式
    audioContextSeconds: 30       // 音频上下文时长（秒）
  });

  // 图片分析相关状态
  const [imageState, setImageState] = useState({
    uploadedImage: null,
    selectedModel: '',
    prompt: '请分析这张图片的内容，包括主要元素、场景背景、色彩搭配和整体印象。',
    analysisResult: null
  });
  
  // WebSocket相关
  const wsRef = useRef(null);
  const clientId = useRef(`client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const frameRequestInterval = useRef(null);
  
  // 文件上传引用
  const fileInputRef = useRef(null);

  // 添加调试日志
  const addDebugLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugLog(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
    console.log(`[DEBUG] ${message}`);
  };

  // 初始化
  useEffect(() => {
    addDebugLog('系统初始化开始');
    loadModels();
    connectWebSocket();
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (frameRequestInterval.current) {
        clearInterval(frameRequestInterval.current);
      }
    };
  }, []);

useEffect(() => {
  if (videoState.currentFrame) {
    const blob = new Blob([videoState.currentFrame], { type: 'image/jpeg' });
    const url = URL.createObjectURL(blob);
    if (videoPlayerRef.current) {
      videoPlayerRef.current.src = url;
      videoPlayerRef.current.play();
    }
    addDebugLog('视频帧已设置到播放器');
  }
}, [videoState.currentFrame]);

// 清理帧请求定时器
useEffect(() => {
    return () => {
      if (frameRequestInterval.current) {
        clearInterval(frameRequestInterval.current);
        frameRequestInterval.current = null;
      }
    };
  }, []);

  // 加载可用模型
  const loadModels = async () => {
    try {
      addDebugLog('正在加载模型列表...');
      let backendVideoModels = [];
      let backendImageModels = [];

      // Load video models from backend
      try {
        const videoResponse = await fetch(`${API_BASE_URL}/models/video`);
        if (videoResponse.ok) {
          const videoData = await videoResponse.json();
          // 过滤掉所有包含"mock"的模型ID
          backendVideoModels = (videoData.models || []).filter(model => {
            return !model.id.toLowerCase().includes('mock');
          });
        } else {
          addDebugLog('视频模型接口暂不可用，使用默认配置');
        }
      } catch (error) {
        addDebugLog(`视频模型加载失败: ${error.message}`);
      }

      // Load image models from backend
      try {
        const imageResponse = await fetch(`${API_BASE_URL}/models/image`);
        if (imageResponse.ok) {
          const imageData = await imageResponse.json();
          // 过滤掉所有包含"mock"的模型ID
          backendImageModels = (imageData.models || []).filter(model => {
            return !model.id.toLowerCase().includes('mock');
          });
        } else {
          addDebugLog('图片模型接口暂不可用，使用默认配置');
        }
      } catch (error) {
        addDebugLog(`图片模型加载失败: ${error.message}`);
      }

      // If no models loaded from specific endpoints, try traditional /models
      if (backendVideoModels.length === 0 && backendImageModels.length === 0) {
        try {
          const response = await fetch(`${API_BASE_URL}/models`);
          if (response.ok) {
            const data = await response.json();
            const traditionalModels = data.models || [];
            // 过滤掉所有包含"mock"的模型ID
            const filteredTraditionalModels = traditionalModels.filter(model => {
              return !model.toLowerCase().includes('mock');
            });
            backendVideoModels = filteredTraditionalModels.map(model => ({ id: model, name: model }));
            backendImageModels = filteredTraditionalModels.map(model => ({ id: model, name: model }));
            addDebugLog(`从传统接口加载了 ${filteredTraditionalModels.length} 个模型`);
          }
        } catch (error) {
          addDebugLog(`传统模型接口也无法访问: ${error.message}`);
        }
      }

      // Combine all models
      let allVideoModels = [...backendVideoModels];
      let allImageModels = [...backendImageModels];

      // Add local LLM models if enabled, and ensure no duplicates
      if (localLLMConfig.enabled) {
        const localLLMVideoModelsToAdd = [
          { id: 'smolvlm2-500m', name: 'SmolVLM2-500M-Video-Instruct-GGUF' },
          { id: 'minicpm-v-2_6-gguf', name: 'MiniCPM-V-2_6-gguf' }
        ];

        localLLMVideoModelsToAdd.forEach(localModel => {
          const isDuplicate = allVideoModels.some(existingModel => existingModel.id === localModel.id);
          if (!isDuplicate) {
            allVideoModels.push(localModel);
          }
        });
      }

      setVideoModels(allVideoModels);
      setImageModels(allImageModels);

      // If no model is selected, set a default from the final list
      if (allVideoModels.length > 0 && !videoState.selectedModel) {
        setVideoState(prev => ({ ...prev, selectedModel: allVideoModels[0].id }));
      }
      if (allImageModels.length > 0 && !imageState.selectedModel) {
        setImageState(prev => ({ ...prev, selectedModel: allImageModels[0].id }));
      }

      addDebugLog(`加载了 ${allVideoModels.length} 个视频模型和 ${allImageModels.length} 个图片模型`);

    } catch (error) {
      console.error('加载模型失败:', error);
      addDebugLog(`加载模型失败: ${error.message}`);
      setSystemStatus('模型加载失败，请检查后端服务和配置文件');
    }
  };

  // WebSocket连接
  const connectWebSocket = () => {
    try {
      addDebugLog(`尝试连接WebSocket: ${WS_BASE_URL}/ws/${clientId.current}`);
      
      wsRef.current = new WebSocket(`${WS_BASE_URL}/ws/${clientId.current}`);
  
      wsRef.current.onopen = () => {
        addDebugLog('WebSocket连接已建立');
        setConnectionStatus('connected');
        setSystemStatus('系统连接正常');
        reconnectAttempts.current = 0;
        
        // 发送连接测试消息
        wsRef.current.send(JSON.stringify({
          type: 'ping',
          timestamp: new Date().toISOString()
        }));
      };
      
      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
          addDebugLog(`消息解析失败: ${error.message}`);
        }
      };
      
      wsRef.current.onclose = (event) => {
        addDebugLog(`WebSocket连接已关闭 (代码: ${event.code})`);
        setConnectionStatus('disconnected');
        
        // 自动重连
        if (reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          addDebugLog(`将在3秒后重连 (尝试 ${reconnectAttempts.current}/${maxReconnectAttempts})`);
          setTimeout(connectWebSocket, 3000);
        } else {
          setSystemStatus('WebSocket连接失败，请刷新页面重试');
          addDebugLog('达到最大重连次数，停止重连');
        }
      };
      
      wsRef.current.onerror = (error) => {
        console.error('WebSocket错误:', error);
        addDebugLog(`WebSocket错误: ${error.message}`);
      };
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      addDebugLog(`WebSocket连接失败: ${error.message}`);
    }
  };

  // 处理WebSocket消息
  const handleWebSocketMessage = (message) => {
    addDebugLog(`收到消息: ${message.type}`);
    
    switch (message.type) {
      case 'connection_established':
        addDebugLog('连接确认消息已收到');
        break;
        
      case 'pong':
        addDebugLog('心跳响应已收到');
        break;
        
      case 'analysis_result':
        addDebugLog(`分析结果: ${message.result?.success ? '成功' : '失败'}`);
        setAnalysisResults(prev => [...prev, { 
          ...message.result, 
          type: 'video',
          timestamp: new Date().toISOString()
        }]);
        
        // 语音播报
        if (message.result.success && message.result.text) {
          speakText(message.result.text);
        }
        break;
        
      case 'video_frame':
        try {
          const frameData = message.frameData;
          if (frameData) {
            setVideoState(prev => ({ 
              ...prev, 
              currentFrame: frameData 
            }));
            // 只在调试开启时记录帧信息，避免日志过多
            if (debugLog.length < 5) {
              addDebugLog('收到新视频帧');
            }
          }
        } catch (error) {
          console.error('处理视频帧失败:', error);
          addDebugLog(`处理视频帧失败: ${error.message}`);
        }
        break;
        
      case 'status_update':
        addDebugLog(`状态更新: ${message.status}`);
        setSystemStatus(message.status);
        break;
        
      case 'transcription_result':
        addDebugLog(`音频转录: ${message.result?.success ? '成功' : '失败'}`);
        if (message.result?.success && message.result?.text) {
          setVideoState(prev => ({
            ...prev,
            transcriptionHistory: [...prev.transcriptionHistory, {
              timestamp: message.result.timestamp || new Date().toISOString(),
              text: message.result.text,
              language: message.result.language || 'auto'
            }]
          }));
        }
        break;

      case 'multimodal_analysis_result':
        addDebugLog(`多模态分析结果: ${message.result?.success ? '成功' : '失败'}`);
        setAnalysisResults(prev => [...prev, {
          ...message.result,
          type: 'multimodal',
          timestamp: new Date().toISOString()
        }]);

        // 语音播报多模态分析结果
        if (message.result.success && message.result.text) {
          speakText(message.result.text);
        }
        break;

      case 'error':
        addDebugLog(`服务器错误: ${message.error || '未知错误'}`);
        setSystemStatus(`服务器错误: ${message.error || '未知错误'}`);
        break;

      default:
        addDebugLog(`未知消息类型: ${message.type}`);
    }
  };

  // 语音播报
  const speakText = (text) => {
    if ('speechSynthesis' in window) {
      try {
        // 停止当前播放
        window.speechSynthesis.cancel();
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'zh-CN';
        utterance.rate = 0.8;
        utterance.pitch = 1;
        utterance.volume = 0.8;
        
        utterance.onstart = () => addDebugLog('开始语音播报');
        utterance.onend = () => addDebugLog('语音播报结束');
        utterance.onerror = (event) => addDebugLog(`语音播报错误: ${event.error}`);
        
        window.speechSynthesis.speak(utterance);
      } catch (error) {
        addDebugLog(`语音播报失败: ${error.message}`);
      }
    } else {
      addDebugLog('浏览器不支持语音播报');
    }
  };

  // 启动帧请求定时器
  const startFramePushing = (sessionId) => {
    if (frameRequestInterval.current) {
      clearInterval(frameRequestInterval.current);
    }
    
    // 立即请求第一帧
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN && sessionId) {
      addDebugLog('立即请求第一帧');
      wsRef.current.send(JSON.stringify({
        type: 'request_frame',
        session_id: sessionId,
        timestamp: new Date().toISOString()
      }));
    }
    
    frameRequestInterval.current = setInterval(() => {
      if (videoState.isVideoPlaying && videoState.currentSession === sessionId) {
        requestCurrentFrame();
      } else {
        clearInterval(frameRequestInterval.current);
        frameRequestInterval.current = null;
      }
    }, 100); // 每100毫秒请求一次帧（提高频率以获取更流畅的显示）
    
    addDebugLog('启动高频率帧推送定时器 (100ms)');
  };
  // 停止帧请求定时器
  const stopFramePushing = () => {
    if (frameRequestInterval.current) {
      clearInterval(frameRequestInterval.current);
      frameRequestInterval.current = null;
      addDebugLog('停止帧推送定时器');
    }
  };

  // 测试本地大模型API连接
  const testLocalLLMConnection = async () => {
    if (!localLLMConfig.apiUrl) {
      alert('请先输入API地址');
      return; 
    }
    
    addDebugLog(`正在测试本地大模型API连接: ${localLLMConfig.apiUrl}`);
    setLoading(true);
    
    try {
      // 尝试请求模型列表API，这是大多数兼容OpenAI API的端点都支持的
      const response = await fetch(`${localLLMConfig.apiUrl}/v1/models`, {
        method: 'GET'
      });
      
      if (response.ok) {
        const data = await response.json();
        
        // 检查是否有模型数据
        if (data.data && data.data.length > 0) {
          const modelNames = data.data.map(model => model.id).join(', ');
          addDebugLog(`API连接成功! 可用模型: ${modelNames}`);
          setSystemStatus(`本地大模型API连接成功，发现 ${data.data.length} 个模型`);
          alert(`连接成功! 在本地服务器上发现 ${data.data.length} 个可用模型。`);
        } else {
          addDebugLog(`API连接成功，但未返回模型列表`);
          setSystemStatus('本地大模型API连接成功，但未发现模型');
          alert('API连接成功，但未找到模型信息。请确认服务器配置正确。');
        }
      } else {
        // 如果模型API调用失败，尝试健康检查端点
        try {
          const healthResponse = await fetch(`${localLLMConfig.apiUrl}/health`);
          if (healthResponse.ok) {
            addDebugLog('API连接部分成功: 健康检查API响应正常，但模型API无法访问');
            setSystemStatus('本地大模型API部分连接: 健康检查正常');
            alert('API服务器可以访问，但模型API端点不可用。请确保服务器支持OpenAI兼容API。');
          } else {
            throw new Error('健康检查API也不可用');
          }
        } catch (healthError) {
          throw new Error(`模型API请求失败: ${response.status} - ${await response.text()}`);
        }
      }
    } catch (error) {
      console.error('API连接测试失败:', error);
      addDebugLog(`API连接失败: ${error.message}`);
      setSystemStatus(`本地大模型API连接失败: ${error.message}`);
      alert(`连接失败: ${error.message}\n请检查地址是否正确以及服务器是否已启动。`);
    } finally {
      setLoading(false);
    }
  };

  // 添加一个转码按钮和状态显示
  const renderStreamControls = () => {
    const isAnyAnalysisActive = videoState.isStreaming || localLLMConfig.isProcessing;
    return (
      <div className="stream-controls">
        <div className="form-group">
          <label htmlFor="streamUrl">视频流地址</label>
          <input
            type="text"
            id="streamUrl"
            className="form-control"
            value={videoState.streamUrl}
            onChange={(e) => setVideoState(prev => ({ ...prev, streamUrl: e.target.value }))}
            placeholder="输入RTSP或HTTP视频流地址"
            disabled={loading || videoState.isVideoPlaying || isAnyAnalysisActive}
          />
          <small className="form-text text-muted">
            支持RTSP地址 (rtsp://) 或HTTP流地址 (http://)
          </small>
        </div>
        
        <div className="form-check mb-3">
          <input
            type="checkbox"
            className="form-check-input"
            id="useTranscoder"
            checked={videoState.useTranscoder}
            onChange={(e) => setVideoState(prev => ({
              ...prev,
              useTranscoder: e.target.checked,
              transcoderUrl: e.target.checked ? prev.transcoderUrl : ''
            }))}
            disabled={loading || videoState.isVideoPlaying || isAnyAnalysisActive}
          />
          <label className="form-check-label" htmlFor="useTranscoder">
            使用RTSP转码器 (推荐用于解决兼容性问题)
          </label>
        </div>

        <div className="form-check mb-3">
          <input
            type="checkbox"
            className="form-check-input"
            id="enableAudio"
            checked={videoState.audioEnabled}
            onChange={(e) => setVideoState(prev => ({
              ...prev,
              audioEnabled: e.target.checked
            }))}
            disabled={loading || videoState.isVideoPlaying || isAnyAnalysisActive}
          />
          <label className="form-check-label" htmlFor="enableAudio">
            启用音频处理 (支持语音转录和多模态分析)
          </label>
        </div>
        
        {videoState.useTranscoder && (
          <div className="form-group mb-3">
            <label htmlFor="transcoderUrl">转码后地址</label>
            <input
              type="text"
              id="transcoderUrl"
              className="form-control"
              value={videoState.transcoderUrl}
              onChange={(e) => setVideoState(prev => ({ ...prev, transcoderUrl: e.target.value }))}
              placeholder="http://localhost:端口/stream.mjpeg"
              disabled={loading || videoState.isVideoPlaying || isAnyAnalysisActive}
            />
            <small className="form-text text-muted">
              运行 <code>python rtsp_transcoder.py {videoState.streamUrl}</code> 获取转码地址
            </small>
          </div>
        )}
        
        <div className="btn-group mb-3">
          {!videoState.isVideoPlaying ? (
            <button
              className="btn btn-primary"
              onClick={openVideo}
              disabled={loading || (!videoState.streamUrl && !(videoState.useTranscoder && videoState.transcoderUrl))}
            >
              {loading && !videoState.sessionId ? '打开中...' : '打开视频'}
            </button>
          ) : (
            <button
              className="btn btn-danger"
              onClick={closeVideo}
              disabled={loading || isAnyAnalysisActive}
            >
              {loading && videoState.sessionId ? '关闭中...' : '关闭视频'}
            </button>
          )}
        </div>
      </div>
    );
  };

  // 修改openVideo函数以支持转码器
  const openVideo = async () => {
    // 确定要使用的URL
    const streamUrl = videoState.useTranscoder && videoState.transcoderUrl
      ? videoState.transcoderUrl 
      : videoState.streamUrl;
      
    if (!streamUrl) {
      alert('请输入视频流地址');
      return;
    }

    setLoading(true);
    addDebugLog(`正在打开视频流: ${streamUrl}`);
    
    try {
      // 如果是RTSP URL且启用了直接流，创建转码器
      if (streamUrl.startsWith('rtsp://') && videoState.showDirectStream) {
        try {
          addDebugLog('创建RTSP转码器用于直接显示');
          const transcoderResponse = await fetch(`${API_BASE_URL}/rtsp/transcode`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              rtsp_url: streamUrl
            })
          });
          
          if (transcoderResponse.ok) {
            const transcoderData = await transcoderResponse.json();
            const mjpegUrl = transcoderData.http_url;
            addDebugLog(`转码器创建成功，MJPEG URL: ${mjpegUrl}`);
            setVideoState(prev => ({ ...prev, mjpegUrl, directStreamEnabled: true }));
          } else {
            addDebugLog('转码器创建失败，将仅使用WebSocket获取帧');
          }
        } catch (error) {
          console.error('创建转码器失败:', error);
          addDebugLog(`创建转码器失败: ${error.message}`);
        }
      }
      
      // 正常打开视频流
      const response = await fetch(`${API_BASE_URL}/video/open`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rtsp_url: streamUrl,
          enable_audio: videoState.audioEnabled
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        setVideoState(prev => ({
          ...prev,
          currentSession: data.session_id,
          isVideoPlaying: true,
          currentFrame: null,
          audioStatus: data.audio_status || null
        }));
        addDebugLog(`视频已打开，会话ID: ${data.session_id}`);

        if (data.audio_enabled) {
          addDebugLog(`音频已启用，状态: ${JSON.stringify(data.audio_status)}`);
        }

        if (data.warning) {
          addDebugLog(`警告: ${data.warning}`);
        }

        setSystemStatus(`视频播放中 (${data.connection_status || '未知状态'})`);

        // 启动帧推送
        startFramePushing(data.session_id);
      } else {
        throw new Error(data.detail || '打开视频失败');
      }
    } catch (error) {
      console.error('打开视频失败:', error);
      addDebugLog(`打开视频失败: ${error.message}`);
      
      // 如果后端接口不存在，尝试使用原有的video/start接口
      try {
        addDebugLog('尝试使用备用接口启动视频...');
        const response = await fetch(`${API_BASE_URL}/video/start`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            rtsp_url: streamUrl,
            model_name: videoState.selectedModel || 'mock-model',
            prompt: videoState.prompt,
            sampling_mode: videoState.samplingMode
          })
        });

        const data = await response.json();
        
        if (response.ok) {
          setVideoState(prev => ({ 
            ...prev, 
            currentSession: data.session_id,
            isVideoPlaying: true,
            currentFrame: null
          }));
          addDebugLog(`使用备用接口启动成功，会话ID: ${data.session_id}`);
          setSystemStatus('视频播放中');
          startFramePushing(data.session_id);
        } else {
          throw new Error(data.detail || '启动视频失败');
        }
      } catch (backupError) {
        setSystemStatus(`打开视频失败: ${error.message}`);
        alert(`打开视频失败: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const closeVideo = async () => {
    if (!videoState.currentSession) return;

    setLoading(true);
    addDebugLog('正在关闭视频...');

    try {
      // 停止帧推送
      stopFramePushing();

      const response = await fetch(`${API_BASE_URL}/video/close/${videoState.currentSession}`, {
        method: 'POST'
      });

      if (response.ok) {
        setVideoState(prev => ({
          ...prev,
          currentSession: null,
          isVideoPlaying: false,
          isStreaming: false,
          currentFrame: null,
          audioStatus: null,
          transcriptionActive: false,
          transcriptionHistory: []
        }));
        addDebugLog('视频已关闭');
        setSystemStatus('视频已关闭');
      } else {
        // 如果close接口不存在，尝试使用stop接口
        const stopResponse = await fetch(`${API_BASE_URL}/video/stop/${videoState.currentSession}`, {
          method: 'POST'
        });

        if (stopResponse.ok) {
          setVideoState(prev => ({
            ...prev,
            currentSession: null,
            isVideoPlaying: false,
            isStreaming: false,
            currentFrame: null,
            audioStatus: null,
            transcriptionActive: false,
            transcriptionHistory: []
          }));
          addDebugLog('视频已停止');
          setSystemStatus('视频已停止');
        } else {
          throw new Error('关闭/停止视频失败');
        }
      }
    } catch (error) {
      console.error('关闭视频失败:', error);
      addDebugLog(`关闭视频失败: ${error.message}`);
      setSystemStatus(`关闭视频失败: ${error.message}`);

      // 强制重置状态
      setVideoState(prev => ({
        ...prev,
        currentSession: null,
        isVideoPlaying: false,
        isStreaming: false,
        currentFrame: null,
        audioStatus: null,
        transcriptionActive: false,
        transcriptionHistory: []
      }));
      stopFramePushing();
    } finally {
      setLoading(false);
    }
  };

  const startVideoAnalysis = async () => {
    if (!videoState.selectedModel || !videoState.prompt) {
      alert('请选择模型并填写提示词');
      return;
    }

    if (!videoState.currentSession) {
      alert('请先打开视频');
      return;
    }

    setLoading(true);
    addDebugLog('正在启动视频分析...');
    
    try {
      // 检查是否是本地大模型
      const isLocalLLMSelected = ['smolvlm2-500m', 'minicpm-v-2_6-gguf'].includes(videoState.selectedModel);
      
      if (localLLMConfig.enabled && isLocalLLMSelected) {
        // 使用本地大模型进行分析
        setLocalLLMConfig(prev => ({
          ...prev,
          isProcessing: true
        }));
        setVideoState(prev => ({
            ...prev,
            isStreaming: true // 也更新视频流分析状态
        }));
        
        addDebugLog(`开始本地大模型分析，间隔: ${localLLMConfig.analysisInterval}ms`);
        setSystemStatus('本地大模型分析中...');
        
        // 立即执行一次分析
        await sendFrameToLocalLLM();
        
        // 清除现有定时器并设置新定时器
        if (localAnalysisIntervalRef.current) {
            clearInterval(localAnalysisIntervalRef.current);
        }
        localAnalysisIntervalRef.current = setInterval(
          sendFrameToLocalLLM, 
          videoState.samplingMode === 'real_time' ? localLLMConfig.analysisInterval : 5000 // on_demand模式固定5秒
        );

      } else {
        // 原有的云端模型分析逻辑
        const response = await fetch(`${API_BASE_URL}/video/analyze/start`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            session_id: videoState.currentSession,
            model_id: videoState.selectedModel,
            prompt: videoState.prompt,
            sampling_mode: videoState.samplingMode
          })
        });

        if (response.ok) {
          const data = await response.json();
          setVideoState(prev => ({ 
            ...prev, 
            isStreaming: true
          }));
          addDebugLog(`视频分析已启动，模式: ${videoState.samplingMode}`);
          setSystemStatus(`视频分析正在运行 (${videoState.samplingMode === 'on_demand' ? '按需采样' : '实时采样'})`);
        } else {
          throw new Error('启动分析失败，接口可能不存在');
        }
      }
    } catch (error) {
      console.error('启动视频分析失败:', error);
      addDebugLog(`启动分析失败: ${error.message}`);
      setSystemStatus(`启动分析失败: ${error.message}`);
      
      // 失败时强制停止所有相关状态
      setVideoState(prev => ({ ...prev, isStreaming: false }));
      setLocalLLMConfig(prev => ({ ...prev, isProcessing: false }));
      if (localAnalysisIntervalRef.current) {
        clearInterval(localAnalysisIntervalRef.current);
        localAnalysisIntervalRef.current = null;
      }
    } finally {
      setLoading(false);
    }
  };

  const stopVideoAnalysis = async () => {
    if (!videoState.currentSession) return;

    setLoading(true);
    addDebugLog('正在停止视频分析...');
    
    try {
      // 检查是否是本地大模型
      const isLocalLLMSelected = ['smolvlm2-500m', 'minicpm-v-2_6-gguf'].includes(videoState.selectedModel);
      
      if (isLocalLLMSelected) {
        // 清除本地大模型分析定时器
        if (localAnalysisIntervalRef.current) {
          clearInterval(localAnalysisIntervalRef.current);
          localAnalysisIntervalRef.current = null;
        }
        
        setLocalLLMConfig(prev => ({
          ...prev,
          isProcessing: false
        }));
        setVideoState(prev => ({
            ...prev,
            isStreaming: false // 也更新视频流分析状态
        }));
        
        addDebugLog('本地大模型分析已停止');
        setSystemStatus('本地大模型分析已停止');
      } else {
        // 原有的云端模型停止逻辑
        const response = await fetch(`${API_BASE_URL}/video/analyze/stop`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            session_id: videoState.currentSession
          })
        });

        if (response.ok) {
          setVideoState(prev => ({ 
            ...prev, 
            isStreaming: false
          }));
          addDebugLog('视频分析已停止，视频继续播放');
          setSystemStatus('视频分析已停止，视频继续播放');
        } else {
          // 如果接口不存在，直接更新状态
          setVideoState(prev => ({ 
            ...prev, 
            isStreaming: false
          }));
          addDebugLog('分析状态已停止');
          setSystemStatus('分析状态已停止');
        }
      }
    } catch (error) {
      console.error('停止视频分析失败:', error);
      addDebugLog(`停止分析失败: ${error.message}`);
      setSystemStatus(`停止分析失败: ${error.message}`);
      
      // 强制停止所有相关状态
      setVideoState(prev => ({ 
        ...prev, 
        isStreaming: false
      }));
      setLocalLLMConfig(prev => ({
        ...prev,
        isProcessing: false
      }));
      if (localAnalysisIntervalRef.current) {
        clearInterval(localAnalysisIntervalRef.current);
        localAnalysisIntervalRef.current = null;
      }
    } finally {
      setLoading(false);
    }
  };

  // 音频控制函数
  const startAudioTranscription = async () => {
    if (!videoState.currentSession) {
      alert('请先打开视频');
      return;
    }

    setLoading(true);
    addDebugLog('正在启动音频转录...');

    try {
      const response = await fetch(`${API_BASE_URL}/audio/transcription/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: videoState.currentSession,
          language: videoState.transcriptionLanguage
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setVideoState(prev => ({ ...prev, transcriptionActive: true }));
        addDebugLog(`音频转录已启动，语言: ${data.language}`);
        setSystemStatus('音频转录中');
      } else {
        throw new Error(data.message || '启动音频转录失败');
      }
    } catch (error) {
      console.error('启动音频转录失败:', error);
      addDebugLog(`启动音频转录失败: ${error.message}`);
      alert(`启动音频转录失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const stopAudioTranscription = async () => {
    if (!videoState.currentSession) return;

    setLoading(true);
    addDebugLog('正在停止音频转录...');

    try {
      const response = await fetch(`${API_BASE_URL}/audio/transcription/stop/${videoState.currentSession}`, {
        method: 'POST'
      });

      if (response.ok) {
        setVideoState(prev => ({ ...prev, transcriptionActive: false }));
        addDebugLog('音频转录已停止');
        setSystemStatus('音频转录已停止');
      } else {
        throw new Error('停止音频转录失败');
      }
    } catch (error) {
      console.error('停止音频转录失败:', error);
      addDebugLog(`停止音频转录失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const analyzeMultimodal = async () => {
    if (!videoState.currentSession) {
      alert('请先打开视频');
      return;
    }

    if (!videoState.selectedModel || !videoState.prompt) {
      alert('请选择模型并填写提示词');
      return;
    }

    setLoading(true);
    addDebugLog('正在进行多模态分析...');

    try {
      const response = await fetch(`${API_BASE_URL}/multimodal/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: videoState.currentSession,
          prompt: videoState.prompt,
          include_recent_audio: true,
          audio_context_seconds: videoState.audioContextSeconds
        })
      });

      const data = await response.json();

      if (response.ok) {
        addDebugLog('多模态分析完成');
        // 结果会通过WebSocket推送，这里只是触发分析
      } else {
        throw new Error(data.detail || '多模态分析失败');
      }
    } catch (error) {
      console.error('多模态分析失败:', error);
      addDebugLog(`多模态分析失败: ${error.message}`);
      alert(`多模态分析失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const refreshTranscriptionHistory = async () => {
    if (!videoState.currentSession) return;

    try {
      const response = await fetch(`${API_BASE_URL}/audio/transcription/history/${videoState.currentSession}?limit=20`);
      const data = await response.json();

      if (response.ok) {
        setVideoState(prev => ({ ...prev, transcriptionHistory: data.history || [] }));
      }
    } catch (error) {
      console.error('获取转录历史失败:', error);
    }
  };

  const analyzeCurrentFrame = async () => {
    if (!videoState.currentSession) {
      alert('请先打开视频');
      return;
    }

    if (!videoState.selectedModel || !videoState.prompt) {
      alert('请选择模型并填写提示词');
      return;
    }

    setLoading(true);
    addDebugLog('正在分析当前帧...');
    
    const isLocalLLMSelected = ['smolvlm2-500m', 'minicpm-v-2_6-gguf'].includes(videoState.selectedModel);

    try {
      if (localLLMConfig.enabled && isLocalLLMSelected) {
        // 本地大模型的单帧分析
        setLocalLLMConfig(prev => ({ ...prev, isProcessing: true })); // 临时设置为处理中

        // 直接调用 sendFrameToLocalLLM
        await sendFrameToLocalLLM();

      } else {
        // 云端模型的单帧分析
        const response = await fetch(`${API_BASE_URL}/video/analyze/frame`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            session_id: videoState.currentSession,
            prompt: videoState.prompt
          })
        });

        if (response.ok) {
          const data = await response.json();
          if (data.result) {
            setAnalysisResults(prev => [...prev, { 
              ...data.result, 
              type: 'video',
              timestamp: new Date().toISOString()
            }]);
            addDebugLog('当前帧分析完成');
            
            // 语音播报
            if (data.result.success && data.result.text) {
              speakText(data.result.text);
            }
          }
        } else {
          // 如果frame接口不存在，尝试使用analyze接口
          const analyzeResponse = await fetch(`${API_BASE_URL}/video/analyze/${videoState.currentSession}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              prompt: videoState.prompt
            })
          });

          if (analyzeResponse.ok) {
            const data = await analyzeResponse.json();
            if (data.result) {
              setAnalysisResults(prev => [...prev, { 
                ...data.result, 
                type: 'video',
                timestamp: new Date().toISOString()
              }]);
              addDebugLog('当前帧分析完成 (使用备用接口)');
              
              // 语音播报
              if (data.result.success && data.result.text) {
                speakText(data.result.text);
              }
            }
          } else {
            throw new Error('分析接口不可用');
          }
        }
      }
    } catch (error) {
      console.error('分析当前帧失败:', error);
      addDebugLog(`分析失败: ${error.message}`);
      alert(`分析失败: ${error.message}`);
    } finally {
      setLoading(false);
      setLocalLLMConfig(prev => ({ ...prev, isProcessing: false })); // 确保单帧分析后重置状态
    }
  };

  const requestCurrentFrame = () => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN && videoState.currentSession) {
      wsRef.current.send(JSON.stringify({
        type: 'request_frame',
        session_id: videoState.currentSession,
        timestamp: new Date().toISOString()
      }));
    } else {
      addDebugLog('WebSocket未连接或会话无效，无法请求帧');
    }
  };

  // 图片分析相关函数
  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      addDebugLog(`选择图片: ${file.name}, 大小: ${(file.size / 1024 / 1024).toFixed(2)}MB`);
      
      // 检查文件大小（限制10MB）
      if (file.size > 10 * 1024 * 1024) {
        alert('文件大小不能超过10MB');
        return;
      }
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setImageState(prev => ({
          ...prev,
          uploadedImage: {
            file: file,
            preview: e.target.result,
            name: file.name
          },
          analysisResult: null
        }));
        addDebugLog('图片预览加载完成');
      };
      reader.onerror = () => {
        addDebugLog('图片读取失败');
        alert('图片读取失败');
      };
      reader.readAsDataURL(file);
    }
  };

  const analyzeImage = async () => {
    if (!imageState.uploadedImage || !imageState.selectedModel || !imageState.prompt) {
      alert('请选择图片并填写完整信息');
      return;
    }

    setLoading(true);
    addDebugLog('正在分析图片...');
    
    try {
      const formData = new FormData();
      formData.append('file', imageState.uploadedImage.file);
      formData.append('model_id', imageState.selectedModel);
      formData.append('prompt', imageState.prompt);

      const response = await fetch(`${API_BASE_URL}/image/analyze`, {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      
      if (response.ok) {
        setImageState(prev => ({ ...prev, analysisResult: data }));
        
        // 添加到统一的分析结果列表
        setAnalysisResults(prev => [...prev, { 
          ...data.result, 
          type: 'image',
          timestamp: data.timestamp,
          image_info: data.image_info,
          image_path: data.file_path
        }]);
        
        addDebugLog('图片分析完成');
        
        // 语音播报
        if (data.result && data.result.success && data.result.text) {
          speakText(data.result.text);
        }
      } else {
        throw new Error(data.detail || '分析失败');
      }
    } catch (error) {
      console.error('图片分析失败:', error);
      addDebugLog(`图片分析失败: ${error.message}`);
      alert(`分析失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 通用函数
  const clearResults = () => {
    setAnalysisResults([]);
    setImageState(prev => ({ ...prev, analysisResult: null }));
    addDebugLog('分析结果已清空');
  };

  const clearDebugLog = () => {
    setDebugLog([]);
  };

  const reconnectWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    reconnectAttempts.current = 0;
    addDebugLog('手动重连WebSocket');
    connectWebSocket();
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN');
  };

  const testConnection = async () => {
    addDebugLog('测试后端连接...');
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      const data = await response.json();
      addDebugLog(`后端连接正常: ${data.status}`);
      setSystemStatus(`后端健康状态: ${data.status}`);
    } catch (error) {
      addDebugLog(`后端连接失败: ${error.message}`);
      setSystemStatus(`后端连接失败: ${error.message}`);
    }
  };

  // 定期心跳检测
  useEffect(() => {
    const heartbeat = setInterval(() => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({
          type: 'ping',
          timestamp: new Date().toISOString()
        }));
      }
    }, 30000); // 每30秒发送一次心跳

    return () => clearInterval(heartbeat);
  }, []);

  // 移除本地大模型设置UI中的分析按钮，统一到主操作按钮
  const renderLocalLLMSettings = () => {
    return (
      <div className="local-llm-settings">
        <h3>本地大模型配置</h3>
        <div className="form-check mb-3">
          <input
            type="checkbox"
            className="form-check-input"
            id="enableLocalLLM"
            checked={localLLMConfig.enabled}
            onChange={(e) => setLocalLLMConfig(prev => ({
              ...prev,
              enabled: e.target.checked
            }))}
          />
          <label className="form-check-label" htmlFor="enableLocalLLM">
            使用本地大模型分析 (llama.cpp兼容API)
          </label>
        </div>
        
        {localLLMConfig.enabled && (
          <>
            <div className="form-group mb-3">
              <label htmlFor="localLLMApiUrl">API地址</label>
              <input
                type="text"
                id="localLLMApiUrl"
                className="form-control"
                value={localLLMConfig.apiUrl}
                onChange={(e) => setLocalLLMConfig(prev => ({
                  ...prev,
                  apiUrl: e.target.value
                }))}
                placeholder="http://localhost:8080"
              />
              <small className="form-text text-muted">
                本地LLM API地址 (需要支持OpenAI兼容的/v1/chat/completions端点)
              </small>
            </div>
            
            <div className="local-llm-guide">
              <p><strong>本地大模型配置指南:</strong></p>
              <ol>
                <li>下载并安装 <a href="https://github.com/ggerganov/llama.cpp" target="_blank" rel="noopener noreferrer">llama.cpp</a></li>
                <li>下载支持视觉的多模态模型，如 LLaVA、Qwen-VL 或 CogVLM 等</li>
                <li>使用 OpenAI 兼容模式启动服务器: 
                  <pre>./server -m models/你的视觉模型.gguf --multimodal-projector models/投影器.gguf -c 2048 --host 0.0.0.0 --port 8080</pre>
                </li>
                <li>确保服务器支持 <code>/v1/chat/completions</code> API和视觉分析功能</li>
              </ol>
              <p className="example-curl">
                测试API可用性:
                <pre>curl http://localhost:8080/v1/models</pre>
              </p>
            </div>
            
            <div className="form-group mb-3">
              <label htmlFor="localLLMMaxTokens">最大生成Token数</label>
              <input
                type="number"
                id="localLLMMaxTokens"
                className="form-control"
                value={localLLMConfig.maxTokens}
                onChange={(e) => setLocalLLMConfig(prev => ({
                  ...prev,
                  maxTokens: parseInt(e.target.value) || 100
                }))}
                min="10"
                max="1000"
              />
            </div>
            
            <div className="form-group mb-3">
              <label htmlFor="localLLMInterval">分析间隔(毫秒)</label>
              <select
                id="localLLMInterval"
                className="form-control"
                value={localLLMConfig.analysisInterval}
                onChange={(e) => setLocalLLMConfig(prev => ({
                  ...prev,
                  analysisInterval: parseInt(e.target.value)
                }))}
                disabled={localLLMConfig.isProcessing}
              >
                <option value="500">500ms</option>
                <option value="1000">1秒</option>
                <option value="2000">2秒</option>
                <option value="5000">5秒</option>
                <option value="10000">10秒</option>
              </select>
            </div>
            
            <div className="btn-group mb-3">
              {/* 本地LLM的分析按钮现在统一到主要操作按钮组 */}
              <button
                className="btn btn-info ml-2"
                onClick={() => testLocalLLMConnection()}
                disabled={loading || localLLMConfig.isProcessing}
              >
                测试API连接
              </button>
            </div>
          </>
        )}
      </div>
    );
  };

  // 发送当前视频帧到本地大模型进行分析
  // 这个函数现在由startVideoAnalysis和analyzeCurrentFrame直接调用，不再管理isProcessing状态
  const sendFrameToLocalLLM = async () => {
    if (!localLLMConfig.enabled) {
      addDebugLog("本地LLM未启用，无法发送帧");
      return;
    }
    
    try {
      addDebugLog('尝试从后端获取当前视频帧...');
      // 获取当前帧
      let currentFrame = null;
      
      // 优先从后端获取最新帧，避免iframe的跨域问题
      const frameResponse = await fetch(`${API_BASE_URL}/video/frame/${videoState.currentSession}`);
      if (frameResponse.ok) {
        const frameData = await frameResponse.json();
        if (frameData.frame) {
          currentFrame = `data:image/jpeg;base64,${frameData.frame}`;
          addDebugLog("成功从后端获取到当前帧");
        } else {
          addDebugLog("后端未返回视频帧数据");
        }
      } else {
        addDebugLog(`从后端获取视频帧失败: ${frameResponse.statusText}`);
        throw new Error('从后端获取视频帧失败');
      }

      if (!currentFrame) {
        addDebugLog("无法获取到有效的视频帧数据");
        return;
      }
      
      addDebugLog("正在准备发送帧到本地大模型API...");
      
      // 确定要发送的模型名称
      const modelToSend = videoState.selectedModel === 'smolvlm2-500m' ? 'SmolVLM2-500M-Video-Instruct-GGUF' : 'MiniCPM-V-2_6-gguf';
      addDebugLog(`发送请求到LLM: ${localLLMConfig.apiUrl}/v1/chat/completions, 模型: ${modelToSend}`);

      // 发送到本地大模型API (OpenAI兼容格式)
      const response = await fetch(`${localLLMConfig.apiUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: modelToSend,
          max_tokens: localLLMConfig.maxTokens,
          messages: [
            { role: 'user', content: [
              { type: 'text', text: videoState.prompt }, 
              { type: 'image_url', image_url: { url: currentFrame } }
            ]}
          ],
          temperature: 0.7 // 添加temperature参数
        })
      });
      
      addDebugLog(`收到LLM API响应，状态: ${response.status}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`LLM API错误: ${response.status} - ${errorData.error || '未知错误'}`);
      }
      
      const data = await response.json();
      addDebugLog('成功解析LLM API响应');

      if (data.choices && data.choices[0] && data.choices[0].message) {
        const result = data.choices[0].message.content;
        
        addDebugLog(`LLM分析结果: ${result.substring(0, 50)}...`); // 截取部分结果以避免日志过长
        
        // 添加分析结果
        setAnalysisResults(prev => [...prev, {
          success: true,
          model: modelToSend,
          type: 'video',
          timestamp: new Date().toISOString(),
          text: result,
          sampling_mode: "local_llm" // 采样模式设为local_llm
        }]);
        
        // 语音播报
        if (result) {
          speakText(result);
        }
        
        addDebugLog("本地大模型分析完成");
      } else {
        addDebugLog("大模型返回结果格式不正确或缺少choices/message");
        throw new Error("大模型返回结果格式不正确或缺少choices/message");
      }
      
    } catch (error) {
      console.error('本地大模型分析失败:', error);
      addDebugLog(`本地大模型分析失败: ${error.message}`);
      
      setAnalysisResults(prev => [...prev, {
        success: false,
        model: videoState.selectedModel === 'smolvlm2-500m' ? 'SmolVLM2-500M-Video-Instruct-GGUF' : 'MiniCPM-V-2_6-gguf',
        type: 'video',
        timestamp: new Date().toISOString(),
        text: `分析失败: ${error.message}`,
        sampling_mode: "local_llm"
      }]);
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>🎥 视频图像智能分析系统</h1>
        <div className="status-bar">
          <div className="connection-status">
            WebSocket: 
            <span className={`status ${connectionStatus}`}>
              {connectionStatus === 'connected' ? '已连接' : 
               connectionStatus === 'error' ? '错误' : '未连接'}
            </span>
            {connectionStatus !== 'connected' && (
              <button onClick={reconnectWebSocket} className="btn-small">
                重连
              </button>
            )}
          </div>
          <div className="connection-status">
            <button onClick={testConnection} className="btn-small">
              测试连接
            </button>
          </div>
          {systemStatus && (
            <div className="system-status">
              状态: {systemStatus}
            </div>
          )}
        </div>
      </header>

      <main className="main-content">
        {/* Tab导航 */}
        <section className="tab-navigation">
          <div className="tab-buttons">
            <button 
              className={`tab-button ${activeTab === 'video' ? 'active' : ''}`}
              onClick={() => setActiveTab('video')}
            >
              📹 视频流分析
            </button>
            <button 
              className={`tab-button ${activeTab === 'image' ? 'active' : ''}`}
              onClick={() => setActiveTab('image')}
            >
              🖼️ 图片分析
            </button>
          </div>
        </section>

        {/* 视频分析Tab */}
        {activeTab === 'video' && (
          <section className="tab-content video-tab">
            <h2>📹 视频流分析</h2>
            
            {renderStreamControls()}

            {/* 视频播放窗口 */}
            <div className="video-player-section">
              <h3>视频播放窗口</h3>
              
              {/* 直接流显示切换 */}
              <div className="stream-toggle">
                <label>
                  <input
                    type="checkbox"
                    checked={videoState.showDirectStream}
                    onChange={(e) => setVideoState(prev => ({ 
                      ...prev, 
                      showDirectStream: e.target.checked 
                    }))}
                  />
                  启用直接流显示
                </label>
              </div>
              
              {/* 显示视频内容 */}
              {videoState.directStreamEnabled && videoState.showDirectStream && videoState.mjpegUrl ? (
                <div className="direct-stream-container">
                  <iframe
                    src={videoState.mjpegUrl}
                    title="RTSP Stream"
                    style={{
                      width: '100%',
                      height: '480px',
                      border: '2px solid #333',
                      borderRadius: '8px',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
                    }}
                  ></iframe>
                  <div className="stream-info">
                    <p>直接流地址: <a href={videoState.mjpegUrl} target="_blank" rel="noopener noreferrer">{videoState.mjpegUrl}</a></p>
                    <button 
                      onClick={() => navigator.clipboard.writeText(videoState.mjpegUrl)}
                      className="btn-small"
                    >
                      复制链接
                    </button>
                  </div>
                </div>
              ) : (
                // 默认的WebSocket帧显示
                videoState.currentFrame ? (
                  <div className="video-frame">
                    <img 
                      src={`data:image/jpeg;base64,${videoState.currentFrame}`} 
                      alt="视频帧" 
                      style={{
                        maxWidth: '100%', 
                        maxHeight: '70vh', 
                        height: 'auto', 
                        borderRadius: '8px',
                        border: '2px solid #333',
                        boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
                      }}
                      onError={(e) => {
                        addDebugLog('视频帧显示失败');
                        console.error('视频帧加载错误', e);
                      }}
                    />
                  </div>
                ) : (
                  <div className="video-frame placeholder" style={{
                    minHeight: '300px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'column',
                    border: '2px dashed #ccc',
                    borderRadius: '8px',
                    padding: '20px'
                  }}>
                    <p>
                      {!videoState.isVideoPlaying 
                        ? '请输入RTSP地址并点击"打开视频"' 
                        : '等待视频帧数据...'}
                    </p>
                    {videoState.isVideoPlaying && (
                      <button onClick={requestCurrentFrame} className="btn-secondary">
                        📷 手动请求帧
                      </button>
                    )}
                  </div>
                )
              )}
            </div>

            {/* 音频控制界面 */}
            {videoState.isVideoPlaying && videoState.audioEnabled && (
              <div className="audio-controls-section">
                <h3>🎤 音频控制</h3>

                {/* 音频状态显示 */}
                <div className="audio-status">
                  <p>音频状态: {videoState.audioStatus ? JSON.stringify(videoState.audioStatus) : '未知'}</p>
                </div>

                {/* 转录控制 */}
                <div className="transcription-controls">
                  <div className="form-group">
                    <label htmlFor="transcriptionLanguage">转录语言:</label>
                    <select
                      id="transcriptionLanguage"
                      className="form-control"
                      value={videoState.transcriptionLanguage}
                      onChange={(e) => setVideoState(prev => ({ ...prev, transcriptionLanguage: e.target.value }))}
                      disabled={loading || videoState.transcriptionActive}
                    >
                      <option value="auto">自动检测</option>
                      <option value="zh">中文</option>
                      <option value="en">英文</option>
                      <option value="yue">粤语</option>
                      <option value="ja">日语</option>
                      <option value="ko">韩语</option>
                    </select>
                  </div>

                  <div className="btn-group mb-3">
                    {!videoState.transcriptionActive ? (
                      <button
                        className="btn btn-success"
                        onClick={startAudioTranscription}
                        disabled={loading}
                      >
                        🎙️ 开始转录
                      </button>
                    ) : (
                      <button
                        className="btn btn-warning"
                        onClick={stopAudioTranscription}
                        disabled={loading}
                      >
                        ⏹️ 停止转录
                      </button>
                    )}
                    <button
                      className="btn btn-info"
                      onClick={refreshTranscriptionHistory}
                      disabled={loading}
                    >
                      🔄 刷新历史
                    </button>
                  </div>
                </div>

                {/* 转录历史 */}
                {videoState.transcriptionHistory.length > 0 && (
                  <div className="transcription-history">
                    <h4>转录历史</h4>
                    <div className="transcription-list">
                      {videoState.transcriptionHistory.slice(-5).map((item, index) => (
                        <div key={index} className="transcription-item">
                          <span className="timestamp">{item.timestamp}</span>
                          <span className="text">{item.text}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 多模态分析控制 */}
                <div className="multimodal-controls">
                  <h4>🔄 多模态分析</h4>
                  <div className="form-group">
                    <label htmlFor="audioContextSeconds">音频上下文时长 (秒):</label>
                    <input
                      type="number"
                      id="audioContextSeconds"
                      className="form-control"
                      value={videoState.audioContextSeconds}
                      onChange={(e) => setVideoState(prev => ({ ...prev, audioContextSeconds: parseInt(e.target.value) || 30 }))}
                      min="5"
                      max="300"
                      disabled={loading}
                    />
                    <small className="form-text text-muted">
                      分析时包含的音频转录上下文时长
                    </small>
                  </div>

                  <button
                    className="btn btn-primary"
                    onClick={analyzeMultimodal}
                    disabled={loading || !videoState.selectedModel || !videoState.prompt}
                  >
                    🚀 多模态分析 (视频+音频)
                  </button>
                </div>
              </div>
            )}

            {/* 视频打开后显示以下控件 */}
            {videoState.isVideoPlaying && (
              <>
                {/* 采样模式 */}
                <div className="form-group">
                  <label>采样模式:</label>
                  <div className="radio-group">
                    <label>
                      <input
                        type="radio"
                        value="on_demand"
                        checked={videoState.samplingMode === 'on_demand'}
                        onChange={(e) => setVideoState(prev => ({ ...prev, samplingMode: e.target.value }))}
                        disabled={loading || videoState.isStreaming || localLLMConfig.isProcessing}
                      />
                      按需采样
                      <span className="mode-description">
                        (视频截帧，选择代表性帧进行分析)
                      </span>
                    </label>
                    <label>
                      <input
                        type="radio"
                        value="real_time"
                        checked={videoState.samplingMode === 'real_time'}
                        onChange={(e) => setVideoState(prev => ({ ...prev, samplingMode: e.target.value }))}
                        disabled={loading || videoState.isStreaming || localLLMConfig.isProcessing}
                      />
                      实时采样
                      <span className="mode-description">
                        (持续分析视频流)
                      </span>
                    </label>
                  </div>
                </div>

                {/* 选择模型 */}
                <div className="form-group">
                  <label>选择模型:</label>
                  <select 
                    value={videoState.selectedModel} 
                    onChange={(e) => {
                      const newModel = e.target.value;
                      setVideoState(prev => ({ ...prev, selectedModel: newModel }));
                    }}
                    disabled={loading || videoState.isStreaming || localLLMConfig.isProcessing}
                  >
                    <option value="">请选择视频分析模型</option>
                    {videoModels.map(model => (
                      <option key={model.id} value={model.id}>{model.name}</option>
                    ))}
                  </select>
                </div>

                {/* 提示词 */}
                <div className="form-group">
                  <label>提示词:</label>
                  <textarea
                    value={videoState.prompt}
                    onChange={(e) => setVideoState(prev => ({ ...prev, prompt: e.target.value }))}
                    placeholder="请输入视频分析提示词..."
                    disabled={loading || videoState.isStreaming || localLLMConfig.isProcessing}
                    rows={3}
                  />
                </div>

                {/* 操作按钮 */}
                <div className="button-group">
                  {/* 主要的分析启动/停止按钮 */}
                  {!videoState.isStreaming && !localLLMConfig.isProcessing ? (
                    <button 
                      onClick={startVideoAnalysis} 
                      disabled={loading || !videoState.selectedModel || !videoState.prompt || !videoState.isVideoPlaying || (['smolvlm2-500m', 'minicpm-v-2_6-gguf'].includes(videoState.selectedModel) && !localLLMConfig.enabled)}
                      className="btn-primary"
                    >
                      {loading ? '启动中...' : '🚀 开始视频分析'}
                    </button>
                  ) : (
                    <button 
                      onClick={stopVideoAnalysis} 
                      disabled={loading}
                      className="btn-danger"
                    >
                      {loading ? '停止中...' : '⏹️ 停止分析'}
                    </button>
                  )}
                  
                  {/* "分析当前帧"按钮，仅在按需采样且未进行持续分析时显示 */}
                  {videoState.samplingMode === 'on_demand' && videoState.isVideoPlaying && !videoState.isStreaming && !localLLMConfig.isProcessing && (
                    <button 
                      onClick={analyzeCurrentFrame} 
                      disabled={loading || !videoState.selectedModel || !videoState.prompt || (['smolvlm2-500m', 'minicpm-v-2_6-gguf'].includes(videoState.selectedModel) && !localLLMConfig.enabled)}
                      className="btn-secondary"
                    >
                      {loading ? '分析中...' : '🔍 分析当前帧'}
                    </button>
                  )}
                </div>
              </>
            )}
            
            {/* 本地大模型设置移到此处，使其在视频打开后，但在主要分析按钮之前 */} 
            {videoState.isVideoPlaying && renderLocalLLMSettings()}

            {/* 本地大模型设置已移到主要分析按钮之前，并受 videoState.isVideoPlaying 控制 */}
          </section>
        )}

        {/* 图片分析Tab */}
        {activeTab === 'image' && (
          <section className="tab-content image-tab">
            <h2>🖼️ 图片分析</h2>
            
            {/* 图片分析部分略去 */}
          </section>
        )}

        {/* 调试面板 */}
        <section className="debug-panel">
          <div className="debug-header">
            <h2>🐛 调试信息</h2>
            <button onClick={clearDebugLog} className="btn-small">
              清空日志
            </button>
          </div>
          <div className="debug-log">
            {debugLog.map((log, index) => (
              <div key={index} className="debug-log-item">
                {log}
              </div>
            ))}
            {debugLog.length === 0 && (
              <div className="debug-log-item">暂无调试信息</div>
            )}
          </div>
        </section>

        {/* 分析结果 */}
        <section className="results-panel">
          <div className="results-header">
            <h2>📊 分析结果</h2>
            <button onClick={clearResults} className="btn-secondary">
              🗑️ 清空结果
            </button>
          </div>

          {analysisResults.length > 0 ? (
            <div className="results-list">
              {analysisResults.slice().reverse().map((result, index) => (
                <div key={index} className={`result-item ${result.type}-result`}>
                  <h3>{result.type === 'video' ? '📹 视频分析结果' : '🖼️ 图片分析结果'}</h3>
                  <div className="result-meta">
                    <span>类型: {result.type === 'video' ? '视频' : '图片'}</span>
                    <span>模型: {result.model}</span>
                    {result.frame_count && <span>帧数: {result.frame_count}</span>}
                    {result.sampling_mode && <span>采样: {result.sampling_mode === 'on_demand' ? '按需' : (result.sampling_mode === 'real_time' ? '实时' : '本地LLM')}</span>}
                    <span>时间: {formatTime(result.timestamp)}</span>
                    <span className={`status ${result.success ? 'success' : 'error'}`}>
                      {result.success ? '✅ 成功' : '❌ 失败'}
                    </span>
                  </div>
                  <div className="result-content">
                    {result.text}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-results">
              <p>暂无分析结果</p>
              <p>提示: 选择{activeTab === 'video' ? '视频分析' : '图片分析'}tab开始使用</p>
            </div>
          )}
        </section>
      </main>

      <footer className="footer">
        <p>视频图像智能分析系统 v2.0.0 | 支持多模态AI模型</p>
        <p>当前标签: {activeTab === 'video' ? '视频分析' : '图片分析'} | 视频会话: {videoState.currentSession ? videoState.currentSession.substr(0, 8) + '...' : '无'}</p>
        <p>视频状态: {videoState.isVideoPlaying ? '播放中' : '未播放'} | 分析状态: {videoState.isStreaming ? '分析中' : '未分析'}</p>
      </footer>
    </div>
  );
}

export default App;