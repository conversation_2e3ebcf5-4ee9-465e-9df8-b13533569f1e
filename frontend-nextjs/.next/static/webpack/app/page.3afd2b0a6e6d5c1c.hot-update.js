"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AudioControls__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AudioControls */ \"(app-pages-browser)/./src/components/AudioControls.tsx\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ModelSelector */ \"(app-pages-browser)/./src/components/ModelSelector.tsx\");\n/* harmony import */ var _components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TranscriptionHistory */ \"(app-pages-browser)/./src/components/TranscriptionHistory.tsx\");\n/* harmony import */ var _components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AnalysisResults */ \"(app-pages-browser)/./src/components/AnalysisResults.tsx\");\n/* harmony import */ var _components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MultimodalAnalysis */ \"(app-pages-browser)/./src/components/MultimodalAnalysis.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/store/useAppStore */ \"(app-pages-browser)/./src/store/useAppStore.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _services_websocket__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/websocket */ \"(app-pages-browser)/./src/services/websocket.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    // Canvas引用用于视频显示\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 状态管理\n    const { videoSessions, currentSessionId, audioStatus, transcriptionHistory, availableModels, selectedModel, analysisResults, isLoading, error, wsConnected, addVideoSession, removeVideoSession, setCurrentSession, setAudioStatus, addTranscriptionResult, clearTranscriptionHistory, setAvailableModels, setSelectedModel, addAnalysisResult, clearAnalysisResults, setLoading, setError, setWsConnected, getCurrentSession, getAudioStatus, getTranscriptionHistory } = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore)();\n    // 本地状态\n    const [rtspUrl, setRtspUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rtsp://*************:554/12');\n    const [audioEnabled, setAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentSession = getCurrentSession();\n    const currentAudioStatus = currentSessionId ? getAudioStatus(currentSessionId) : null;\n    // 调试信息\n    console.log('当前状态:', {\n        currentSessionId,\n        currentSession,\n        videoSessions,\n        hasCurrentSession: !!currentSession\n    });\n    const currentTranscriptionHistory = currentSessionId ? getTranscriptionHistory(currentSessionId) : [];\n    // WebSocket 消息处理\n    const handleWebSocketMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[handleWebSocketMessage]\": (message)=>{\n            if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isTranscriptionMessage)(message)) {\n                if (message.sessionId) {\n                    addTranscriptionResult(message.sessionId, message.result);\n                }\n            } else if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isMultimodalAnalysisMessage)(message)) {\n                addAnalysisResult(message.result);\n            }\n        }\n    }[\"HomePage.useCallback[handleWebSocketMessage]\"], [\n        addTranscriptionResult,\n        addAnalysisResult\n    ]);\n    // 视频帧渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            if (!currentSession || !canvasRef.current) return;\n            const canvas = canvasRef.current;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            let animationId;\n            const drawFrame = {\n                \"HomePage.useEffect.drawFrame\": ()=>{\n                    const time = Date.now() / 1000;\n                    // 创建动态背景\n                    const gradient = ctx.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, Math.max(canvas.width, canvas.height) / 2);\n                    gradient.addColorStop(0, '#1e3a8a');\n                    gradient.addColorStop(0.5, '#1e40af');\n                    gradient.addColorStop(1, '#1e293b');\n                    ctx.fillStyle = gradient;\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    // 添加动态粒子效果\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';\n                    for(let i = 0; i < 20; i++){\n                        const x = (Math.sin(time * 0.5 + i) * 0.3 + 0.5) * canvas.width;\n                        const y = (Math.cos(time * 0.3 + i * 0.5) * 0.3 + 0.5) * canvas.height;\n                        const size = Math.sin(time * 2 + i) * 2 + 3;\n                        ctx.beginPath();\n                        ctx.arc(x, y, size, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // 添加扫描线效果\n                    const scanLine = time * 100 % canvas.height;\n                    ctx.fillStyle = 'rgba(0, 255, 0, 0.1)';\n                    ctx.fillRect(0, scanLine, canvas.width, 2);\n                    // 添加中心信息面板\n                    const panelWidth = 400;\n                    const panelHeight = 120;\n                    const panelX = (canvas.width - panelWidth) / 2;\n                    const panelY = (canvas.height - panelHeight) / 2;\n                    // 半透明背景\n                    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';\n                    ctx.fillRect(panelX, panelY, panelWidth, panelHeight);\n                    // 边框\n                    ctx.strokeStyle = 'rgba(34, 197, 94, 0.8)';\n                    ctx.lineWidth = 2;\n                    ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);\n                    // 标题\n                    ctx.fillStyle = '#22c55e';\n                    ctx.font = 'bold 20px Arial';\n                    ctx.textAlign = 'center';\n                    ctx.fillText('RTSP 实时视频流', canvas.width / 2, panelY + 35);\n                    // 信息文本\n                    ctx.font = '14px Arial';\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';\n                    ctx.fillText(\"会话ID: \".concat(currentSession.id.slice(0, 8), \"...\"), canvas.width / 2, panelY + 60);\n                    ctx.fillText(\"时间: \".concat(new Date().toLocaleTimeString()), canvas.width / 2, panelY + 80);\n                    // 状态指示器\n                    const pulse = (Math.sin(time * 3) + 1) / 2;\n                    ctx.fillStyle = \"rgba(34, 197, 94, \".concat(0.5 + pulse * 0.5, \")\");\n                    ctx.beginPath();\n                    ctx.arc(canvas.width / 2 - 80, panelY + 95, 6, 0, Math.PI * 2);\n                    ctx.fill();\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';\n                    ctx.font = '12px Arial';\n                    ctx.textAlign = 'left';\n                    ctx.fillText('● LIVE', canvas.width / 2 - 70, panelY + 100);\n                    animationId = requestAnimationFrame(drawFrame);\n                }\n            }[\"HomePage.useEffect.drawFrame\"];\n            drawFrame();\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    if (animationId) {\n                        cancelAnimationFrame(animationId);\n                    }\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        currentSession\n    ]);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // 连接 WebSocket\n            _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.connect();\n            // 添加消息处理器\n            const removeMessageHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addMessageHandler(handleWebSocketMessage);\n            // 添加连接状态处理器\n            const removeConnectionHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addConnectionHandler({\n                \"HomePage.useEffect.removeConnectionHandler\": (status)=>{\n                    setWsConnected(status === 'connected');\n                }\n            }[\"HomePage.useEffect.removeConnectionHandler\"]);\n            // 加载可用模型\n            loadModels();\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    removeMessageHandler();\n                    removeConnectionHandler();\n                    _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.disconnect();\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        handleWebSocketMessage,\n        setWsConnected\n    ]);\n    // 加载模型列表\n    const loadModels = async ()=>{\n        try {\n            const models = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.getAvailableModels();\n            // 确保 models 是数组\n            const modelArray = Array.isArray(models) ? models : [];\n            setAvailableModels(modelArray);\n            // 自动选择第一个可用的多模态模型\n            const multimodalModel = modelArray.find((m)=>m.type === 'multimodal' && m.isAvailable);\n            if (multimodalModel && !selectedModel) {\n                setSelectedModel(multimodalModel.id);\n            }\n        } catch (error) {\n            console.error('Failed to load models:', error);\n            setError('加载模型列表失败');\n            // 设置默认的模型数据以便测试\n            setAvailableModels([\n                {\n                    id: 'test-multimodal',\n                    name: '测试多模态模型',\n                    type: 'multimodal',\n                    provider: 'Test',\n                    description: '用于测试的模拟模型',\n                    isAvailable: true\n                },\n                {\n                    id: 'test-vision',\n                    name: '测试视觉模型',\n                    type: 'vision',\n                    provider: 'Test',\n                    description: '用于测试的视觉模型',\n                    isAvailable: true\n                }\n            ]);\n        }\n    };\n    // 打开视频流\n    const handleOpenVideo = async ()=>{\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.isValidRtspUrl)(rtspUrl)) {\n            setError('请输入有效的RTSP地址');\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        console.log('开始打开视频流:', rtspUrl);\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.openVideo({\n                rtsp_url: rtspUrl,\n                enable_audio: audioEnabled\n            });\n            console.log('API响应:', response);\n            if (response.success) {\n                const session = {\n                    id: response.session_id,\n                    rtspUrl,\n                    isActive: true,\n                    audioEnabled,\n                    createdAt: new Date()\n                };\n                console.log('创建会话:', session);\n                addVideoSession(session);\n                setCurrentSession(response.session_id);\n                // 设置音频状态\n                if (response.audio_status) {\n                    setAudioStatus(response.session_id, response.audio_status);\n                }\n                console.log('视频流打开成功');\n            } else {\n                console.error('API返回失败:', response);\n                setError(response.message || '打开视频流失败');\n            }\n        } catch (error) {\n            console.error('API调用失败:', error);\n            setError('连接视频流失败，请检查网络和RTSP地址');\n            // 为了测试，我们可以创建一个模拟会话\n            console.log('创建模拟会话进行测试');\n            const mockSession = {\n                id: \"mock-\".concat(Date.now()),\n                rtspUrl,\n                isActive: true,\n                audioEnabled,\n                createdAt: new Date()\n            };\n            addVideoSession(mockSession);\n            setCurrentSession(mockSession.id);\n            setError('使用模拟视频流（后端连接失败）');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 关闭视频流\n    const handleCloseVideo = async ()=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.closeVideo(currentSessionId);\n            removeVideoSession(currentSessionId);\n            setCurrentSession(null);\n        } catch (error) {\n            console.error('Failed to close video:', error);\n            setError('关闭视频流失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 开始音频转录\n    const handleStartTranscription = async (language)=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.startAudioTranscription({\n                session_id: currentSessionId,\n                language\n            });\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: true,\n                    language\n                });\n            }\n        } catch (error) {\n            setError('启动音频转录失败');\n        }\n    };\n    // 停止音频转录\n    const handleStopTranscription = async ()=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.stopAudioTranscription(currentSessionId);\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: false\n                });\n            }\n        } catch (error) {\n            setError('停止音频转录失败');\n        }\n    };\n    // 多模态分析\n    const handleMultimodalAnalysis = async (prompt, includeAudio, contextSeconds)=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.analyzeMultimodal({\n                session_id: currentSessionId,\n                prompt,\n                include_recent_audio: includeAudio,\n                audio_context_seconds: contextSeconds\n            });\n            addAnalysisResult(result);\n        } catch (error) {\n            setError('多模态分析失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"VA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"视频分析系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                        variant: wsConnected ? \"success\" : \"danger\",\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            wsConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 32\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 63\n                                            }, this),\n                                            wsConnected ? '已连接' : '未连接'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        size: \"sm\",\n                                        variant: \"ghost\",\n                                        icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-5 h-5 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>setError(null),\n                                className: \"ml-auto\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    children: \"视频流控制\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        !currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium text-blue-900\",\n                                                                            children: \"第一步：打开视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                                    children: '输入RTSP地址，选择是否启用音频，然后点击\"打开视频流\"按钮'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    label: \"RTSP地址\",\n                                                                    value: rtspUrl,\n                                                                    onChange: (e)=>setRtspUrl(e.target.value),\n                                                                    placeholder: \"rtsp://*************:554/12\",\n                                                                    disabled: !!currentSession\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-end gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center gap-2 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: audioEnabled,\n                                                                                onChange: (e)=>setAudioEnabled(e.target.checked),\n                                                                                disabled: !!currentSession,\n                                                                                className: \"rounded\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"启用音频处理\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: !currentSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleOpenVideo,\n                                                                loading: isLoading,\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"打开视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleCloseVideo,\n                                                                loading: isLoading,\n                                                                variant: \"danger\",\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"关闭视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                            children: \"实时视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"RTSP地址: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-mono text-xs\",\n                                                                                    children: currentSession.rtspUrl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 543,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 542,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative bg-black rounded-lg overflow-hidden aspect-video border-2 border-gray-300 shadow-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                                                            ref: canvasRef,\n                                                                            width: 1280,\n                                                                            height: 720,\n                                                                            className: \"w-full h-full object-contain\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"success\",\n                                                                                className: \"bg-green-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 562,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"实时播放\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 558,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        currentSession.audioEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 right-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"primary\",\n                                                                                className: \"bg-blue-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 574,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"音频已启用\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 570,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 right-4 flex gap-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                                size: \"sm\",\n                                                                                variant: \"secondary\",\n                                                                                className: \"bg-black bg-opacity-50 text-white border-gray-600 hover:bg-opacity-70\",\n                                                                                onClick: ()=>{\n                                                                                    const canvas = canvasRef.current;\n                                                                                    if (canvas) {\n                                                                                        const link = document.createElement('a');\n                                                                                        link.download = \"screenshot-\".concat(Date.now(), \".png\");\n                                                                                        link.href = canvas.toDataURL();\n                                                                                        link.click();\n                                                                                    }\n                                                                                },\n                                                                                children: \"截图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 582,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 581,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-black bg-opacity-70 text-white px-3 py-2 rounded text-sm shadow-md\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"flex items-center gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                                    lineNumber: 605,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                \"会话: \",\n                                                                                                currentSession.id.slice(0, 8),\n                                                                                                \"...\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 604,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"1280x720\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 608,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: new Date().toLocaleTimeString()\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 609,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 603,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 13\n                                    }, this),\n                                    currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-blue-900\",\n                                                                children: \"视频流已打开，现在可以进行智能分析\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                        children: '请输入分析提示词或选择预设提示词，然后点击\"开始分析\"按钮'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__.MultimodalAnalysis, {\n                                                sessionId: currentSessionId,\n                                                audioEnabled: currentSession.audioEnabled || false,\n                                                onAnalyze: handleMultimodalAnalysis,\n                                                disabled: false,\n                                                loading: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, this),\n                                    analysisResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-green-50 border border-green-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-green-900\",\n                                                                children: \"分析结果\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700 mt-2 ml-8\",\n                                                        children: \"大模型实时输出的分析结果如下\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__.AnalysisResults, {\n                                                results: analysisResults,\n                                                onClear: clearAnalysisResults\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__.ModelSelector, {\n                                        models: availableModels,\n                                        selectedModel: selectedModel,\n                                        onModelSelect: setSelectedModel\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 13\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && currentAudioStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioControls__WEBPACK_IMPORTED_MODULE_2__.AudioControls, {\n                                        sessionId: currentSession.id,\n                                        audioStatus: currentAudioStatus,\n                                        onStartTranscription: handleStartTranscription,\n                                        onStopTranscription: handleStopTranscription,\n                                        onLanguageChange: ()=>{}\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 15\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__.TranscriptionHistory, {\n                                        history: currentTranscriptionHistory,\n                                        onClear: ()=>currentSessionId && clearTranscriptionHistory(currentSessionId)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 447,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n        lineNumber: 414,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"Vf8RV70pf4zhRU52G15SAYi/jmE=\", false, function() {\n    return [\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});