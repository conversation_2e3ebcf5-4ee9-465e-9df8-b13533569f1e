'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { VideoPlayer } from '@/components/VideoPlayer';
import { AudioControls } from '@/components/AudioControls';
import { ModelSelector } from '@/components/ModelSelector';
import { TranscriptionHistory } from '@/components/TranscriptionHistory';
import { AnalysisResults } from '@/components/AnalysisResults';
import { MultimodalAnalysis } from '@/components/MultimodalAnalysis';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import {
  Play,
  Square,
  Settings,
  Wifi,
  WifiOff,
  AlertCircle,
  CheckCircle,
  Volume2
} from 'lucide-react';

import { useAppStore } from '@/store/useAppStore';
import { apiService } from '@/services/api';
import { wsService, isTranscriptionMessage, isMultimodalAnalysisMessage, isVideoFrameMessage } from '@/services/websocket';
import { generateId, isValidRtspUrl } from '@/lib/utils';
import type { Language, VideoSession, AudioStatus, TranscriptionResult, MultimodalAnalysisResult } from '@/types';

export default function HomePage() {
  // Canvas引用用于视频显示
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 视频帧请求定时器
  const frameRequestInterval = useRef<NodeJS.Timeout | null>(null);

  // 状态管理
  const {
    videoSessions,
    currentSessionId,
    audioStatus,
    transcriptionHistory,
    availableModels,
    selectedModel,
    analysisResults,
    isLoading,
    error,
    wsConnected,
    addVideoSession,
    removeVideoSession,
    setCurrentSession,
    updateVideoFrame,
    setAudioStatus,
    addTranscriptionResult,
    clearTranscriptionHistory,
    setAvailableModels,
    setSelectedModel,
    addAnalysisResult,
    clearAnalysisResults,
    setLoading,
    setError,
    setWsConnected,
    getCurrentSession,
    getAudioStatus,
    getTranscriptionHistory,
  } = useAppStore();

  // 本地状态
  const [rtspUrl, setRtspUrl] = useState('rtsp://*************:554/12');
  const [audioEnabled, setAudioEnabled] = useState(false);

  const currentSession = getCurrentSession();
  const currentAudioStatus = currentSessionId ? getAudioStatus(currentSessionId) : null;

  // 调试信息
  console.log('当前状态:', {
    currentSessionId,
    currentSession,
    videoSessions,
    hasCurrentSession: !!currentSession
  });
  const currentTranscriptionHistory = currentSessionId ? getTranscriptionHistory(currentSessionId) : [];

  // WebSocket 消息处理
  const handleWebSocketMessage = useCallback((message: any) => {
    if (isTranscriptionMessage(message)) {
      if (message.sessionId) {
        addTranscriptionResult(message.sessionId, message.result);
      }
    } else if (isMultimodalAnalysisMessage(message)) {
      addAnalysisResult(message.result);
    } else if (isVideoFrameMessage(message)) {
      // 处理视频帧数据
      console.log('收到视频帧消息:', {
        sessionId: message.session_id,
        frameDataLength: message.frameData?.length || 0,
        currentSessionId
      });

      if (currentSessionId && message.frameData) {
        console.log('更新视频帧数据');
        updateVideoFrame(currentSessionId, message.frameData);
      } else {
        console.warn('无法更新视频帧:', { currentSessionId, hasFrameData: !!message.frameData });
      }
    }
  }, [addTranscriptionResult, addAnalysisResult, updateVideoFrame, currentSessionId]);

  // 请求视频帧
  const requestVideoFrame = useCallback(() => {
    if (wsService.getConnectionStatus() === 'connected' && currentSessionId) {
      wsService.send({
        type: 'request_frame',
        session_id: currentSessionId,
        timestamp: new Date().toISOString()
      });
    }
  }, [currentSessionId]);

  // 启动帧请求定时器
  const startFrameRequesting = useCallback((sessionId: string) => {
    if (frameRequestInterval.current) {
      clearInterval(frameRequestInterval.current);
    }

    // 立即请求第一帧
    if (wsService.getConnectionStatus() === 'connected') {
      wsService.send({
        type: 'request_frame',
        session_id: sessionId,
        timestamp: new Date().toISOString()
      });
    }

    // 设置定时器每100毫秒请求一次帧
    frameRequestInterval.current = setInterval(() => {
      if (wsService.getConnectionStatus() === 'connected' && currentSessionId === sessionId) {
        requestVideoFrame();
      } else {
        stopFrameRequesting();
      }
    }, 100);

    console.log('启动视频帧请求定时器 (100ms)');
  }, [requestVideoFrame, currentSessionId]);

  // 停止帧请求定时器
  const stopFrameRequesting = useCallback(() => {
    if (frameRequestInterval.current) {
      clearInterval(frameRequestInterval.current);
      frameRequestInterval.current = null;
      console.log('停止视频帧请求定时器');
    }
  }, []);

  // 视频帧渲染
  useEffect(() => {
    if (!currentSession || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let animationId: number;
    let lastFrameData: string | null = null;

    const drawFrame = () => {
      // 如果有新的视频帧数据，绘制真实视频帧
      if (currentSession.currentFrame && currentSession.currentFrame !== lastFrameData) {
        console.log('绘制新视频帧，数据长度:', currentSession.currentFrame.length);
        lastFrameData = currentSession.currentFrame;

        const img = new Image();
        img.onload = () => {
          console.log('图片加载成功，尺寸:', img.width, 'x', img.height);
          // 清除画布
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // 计算保持宽高比的绘制尺寸
          const aspectRatio = img.width / img.height;
          const canvasAspectRatio = canvas.width / canvas.height;

          let drawWidth, drawHeight, drawX, drawY;

          if (aspectRatio > canvasAspectRatio) {
            // 图片更宽，以宽度为准
            drawWidth = canvas.width;
            drawHeight = canvas.width / aspectRatio;
            drawX = 0;
            drawY = (canvas.height - drawHeight) / 2;
          } else {
            // 图片更高，以高度为准
            drawHeight = canvas.height;
            drawWidth = canvas.height * aspectRatio;
            drawX = (canvas.width - drawWidth) / 2;
            drawY = 0;
          }

          // 绘制视频帧
          ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

          // 添加状态信息覆盖层
          drawStatusOverlay(ctx, canvas);
        };

        img.onerror = (error) => {
          console.error('图片加载失败:', error);
          console.log('失败的base64数据前100字符:', currentSession.currentFrame?.substring(0, 100));
        };

        // 设置图片源（base64数据）
        img.src = `data:image/jpeg;base64,${currentSession.currentFrame}`;
      } else if (!currentSession.currentFrame) {
        // 如果没有视频帧数据，显示等待状态
        drawWaitingState(ctx, canvas);
      }

      animationId = requestAnimationFrame(drawFrame);
    };

    // 绘制状态信息覆盖层
    const drawStatusOverlay = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => {
      const time = Date.now() / 1000;

      // 右上角状态信息
      const padding = 10;
      const boxWidth = 200;
      const boxHeight = 60;

      // 半透明背景
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(canvas.width - boxWidth - padding, padding, boxWidth, boxHeight);

      // 边框
      ctx.strokeStyle = 'rgba(34, 197, 94, 0.8)';
      ctx.lineWidth = 1;
      ctx.strokeRect(canvas.width - boxWidth - padding, padding, boxWidth, boxHeight);

      // LIVE 指示器
      const pulse = (Math.sin(time * 3) + 1) / 2;
      ctx.fillStyle = `rgba(255, 0, 0, ${0.5 + pulse * 0.5})`;
      ctx.beginPath();
      ctx.arc(canvas.width - boxWidth - padding + 15, padding + 20, 4, 0, Math.PI * 2);
      ctx.fill();

      // 文本信息
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 12px Arial';
      ctx.textAlign = 'left';
      ctx.fillText('● LIVE', canvas.width - boxWidth - padding + 25, padding + 25);

      ctx.font = '10px Arial';
      ctx.fillText(`会话: ${currentSession.id.slice(0, 8)}...`, canvas.width - boxWidth - padding + 10, padding + 45);
    };

    // 绘制等待状态
    const drawWaitingState = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => {
      const time = Date.now() / 1000;

      // 深色背景
      ctx.fillStyle = '#1a1a1a';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 中心等待信息
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;

      // 旋转的加载指示器
      ctx.save();
      ctx.translate(centerX, centerY - 30);
      ctx.rotate(time * 2);
      ctx.strokeStyle = '#22c55e';
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.arc(0, 0, 20, 0, Math.PI * 1.5);
      ctx.stroke();
      ctx.restore();

      // 等待文本
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('等待视频流数据...', centerX, centerY + 20);

      ctx.font = '12px Arial';
      ctx.fillStyle = '#888888';
      ctx.fillText('请确保RTSP地址正确且网络连接正常', centerX, centerY + 45);
    };

    drawFrame();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [currentSession]);

  // 初始化
  useEffect(() => {
    // 连接 WebSocket
    wsService.connect();
    
    // 添加消息处理器
    const removeMessageHandler = wsService.addMessageHandler(handleWebSocketMessage);
    
    // 添加连接状态处理器
    const removeConnectionHandler = wsService.addConnectionHandler((status) => {
      setWsConnected(status === 'connected');
    });

    // 加载可用模型
    loadModels();

    return () => {
      removeMessageHandler();
      removeConnectionHandler();
      stopFrameRequesting(); // 清理帧请求定时器
      wsService.disconnect();
    };
  }, [handleWebSocketMessage, setWsConnected, stopFrameRequesting]);

  // 加载模型列表
  const loadModels = async () => {
    try {
      const models = await apiService.getAvailableModels();

      // 确保 models 是数组
      const modelArray = Array.isArray(models) ? models : [];
      setAvailableModels(modelArray);

      // 自动选择第一个可用的多模态模型
      const multimodalModel = modelArray.find(m => m.type === 'multimodal' && m.isAvailable);
      if (multimodalModel && !selectedModel) {
        setSelectedModel(multimodalModel.id);
      }
    } catch (error) {
      console.error('Failed to load models:', error);
      setError('加载模型列表失败');

      // 设置默认的模型数据以便测试
      setAvailableModels([
        {
          id: 'test-multimodal',
          name: '测试多模态模型',
          type: 'multimodal',
          provider: 'Test',
          description: '用于测试的模拟模型',
          isAvailable: true
        },
        {
          id: 'test-vision',
          name: '测试视觉模型',
          type: 'vision',
          provider: 'Test',
          description: '用于测试的视觉模型',
          isAvailable: true
        }
      ]);
    }
  };

  // 打开视频流
  const handleOpenVideo = async () => {
    if (!isValidRtspUrl(rtspUrl)) {
      setError('请输入有效的RTSP地址');
      return;
    }

    setLoading(true);
    setError(null);
    console.log('开始打开视频流:', rtspUrl);

    try {
      const response = await apiService.openVideo({
        rtsp_url: rtspUrl,
        enable_audio: audioEnabled,
      });

      console.log('API响应:', response);

      if (response.success) {
        const session: VideoSession = {
          id: response.session_id,
          rtspUrl,
          isActive: true,
          audioEnabled,
          createdAt: new Date(),
        };

        console.log('创建会话:', session);
        addVideoSession(session);
        setCurrentSession(response.session_id);

        // 设置音频状态
        if (response.audio_status) {
          setAudioStatus(response.session_id, response.audio_status);
        }

        // 启动视频帧请求
        startFrameRequesting(response.session_id);

        console.log('视频流打开成功');
      } else {
        console.error('API返回失败:', response);
        setError(response.message || '打开视频流失败');
      }
    } catch (error) {
      console.error('API调用失败:', error);
      setError('连接视频流失败，请检查网络和RTSP地址');

      // 为了测试，我们可以创建一个模拟会话
      console.log('创建模拟会话进行测试');
      const mockSession: VideoSession = {
        id: `mock-${Date.now()}`,
        rtspUrl,
        isActive: true,
        audioEnabled,
        createdAt: new Date(),
      };

      addVideoSession(mockSession);
      setCurrentSession(mockSession.id);

      // 即使是模拟会话也启动帧请求（用于测试）
      startFrameRequesting(mockSession.id);

      setError('使用模拟视频流（后端连接失败）');
    } finally {
      setLoading(false);
    }
  };

  // 关闭视频流
  const handleCloseVideo = async () => {
    if (!currentSessionId) return;

    setLoading(true);

    try {
      // 停止帧请求
      stopFrameRequesting();

      await apiService.closeVideo(currentSessionId);
      removeVideoSession(currentSessionId);
      setCurrentSession(null);
    } catch (error) {
      console.error('Failed to close video:', error);
      setError('关闭视频流失败');
    } finally {
      setLoading(false);
    }
  };

  // 开始音频转录
  const handleStartTranscription = async (language: Language) => {
    if (!currentSessionId) return;

    try {
      await apiService.startAudioTranscription({
        session_id: currentSessionId,
        language,
      });

      // 更新音频状态
      const currentStatus = getAudioStatus(currentSessionId);
      if (currentStatus) {
        setAudioStatus(currentSessionId, {
          ...currentStatus,
          transcriptionActive: true,
          language,
        });
      }
    } catch (error) {
      setError('启动音频转录失败');
    }
  };

  // 停止音频转录
  const handleStopTranscription = async () => {
    if (!currentSessionId) return;

    try {
      await apiService.stopAudioTranscription(currentSessionId);

      // 更新音频状态
      const currentStatus = getAudioStatus(currentSessionId);
      if (currentStatus) {
        setAudioStatus(currentSessionId, {
          ...currentStatus,
          transcriptionActive: false,
        });
      }
    } catch (error) {
      setError('停止音频转录失败');
    }
  };

  // 多模态分析
  const handleMultimodalAnalysis = async (
    prompt: string,
    includeAudio: boolean,
    contextSeconds: number
  ) => {
    if (!currentSessionId) return;

    setLoading(true);

    try {
      const result = await apiService.analyzeMultimodal({
        session_id: currentSessionId,
        prompt,
        include_recent_audio: includeAudio,
        audio_context_seconds: contextSeconds,
      });

      addAnalysisResult(result);
    } catch (error) {
      setError('多模态分析失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">VA</span>
              </div>
              <h1 className="text-xl font-semibold text-gray-900">
                视频分析系统
              </h1>
            </div>
            
            <div className="flex items-center gap-4">
              {/* WebSocket 状态 */}
              <Badge 
                variant={wsConnected ? "success" : "danger"}
                className="flex items-center gap-1"
              >
                {wsConnected ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                {wsConnected ? '已连接' : '未连接'}
              </Badge>
              
              <Button size="sm" variant="ghost" icon={Settings}>
                设置
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setError(null)}
              className="ml-auto"
            >
              ✕
            </Button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧列 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 视频控制 */}
            <Card>
              <CardHeader>
                <CardTitle>视频流控制</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* 步骤指示 */}
                  {!currentSession && (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          1
                        </div>
                        <h3 className="font-medium text-blue-900">
                          第一步：打开视频流
                        </h3>
                      </div>
                      <p className="text-sm text-blue-700 mt-2 ml-8">
                        输入RTSP地址，选择是否启用音频，然后点击"打开视频流"按钮
                      </p>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="RTSP地址"
                      value={rtspUrl}
                      onChange={(e) => setRtspUrl(e.target.value)}
                      placeholder="rtsp://*************:554/12"
                      disabled={!!currentSession}
                    />

                    <div className="flex items-end gap-2">
                      <label className="flex items-center gap-2 text-sm">
                        <input
                          type="checkbox"
                          checked={audioEnabled}
                          onChange={(e) => setAudioEnabled(e.target.checked)}
                          disabled={!!currentSession}
                          className="rounded"
                        />
                        启用音频处理
                      </label>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {!currentSession ? (
                      <Button
                        onClick={handleOpenVideo}
                        loading={isLoading}
                        icon={Play}
                        className="flex-1"
                      >
                        打开视频流
                      </Button>
                    ) : (
                      <Button
                        onClick={handleCloseVideo}
                        loading={isLoading}
                        variant="danger"
                        icon={Square}
                        className="flex-1"
                      >
                        关闭视频流
                      </Button>
                    )}
                  </div>

                  {/* 视频播放区域 - 直接在按钮下方显示 */}
                  {currentSession && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="mb-4">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">实时视频流</h3>
                        <p className="text-sm text-gray-600">
                          RTSP地址: <span className="font-mono text-xs">{currentSession.rtspUrl}</span>
                        </p>
                      </div>

                      {/* 视频显示区域 */}
                      <div className="relative bg-black rounded-lg overflow-hidden aspect-video border-2 border-gray-300 shadow-lg">
                        <canvas
                          ref={canvasRef}
                          width={1280}
                          height={720}
                          className="w-full h-full object-contain"
                        />

                        {/* 视频状态覆盖层 */}
                        <div className="absolute top-4 left-4">
                          <Badge
                            variant="success"
                            className="bg-green-600 text-white flex items-center gap-1 shadow-md"
                          >
                            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                            实时播放
                          </Badge>
                        </div>

                        {/* 音频状态指示 */}
                        {currentSession.audioEnabled && (
                          <div className="absolute top-4 right-4">
                            <Badge
                              variant="primary"
                              className="bg-blue-600 text-white flex items-center gap-1 shadow-md"
                            >
                              <Volume2 className="w-3 h-3" />
                              音频已启用
                            </Badge>
                          </div>
                        )}

                        {/* 视频控制按钮 */}
                        <div className="absolute bottom-4 right-4 flex gap-2">
                          <Button
                            size="sm"
                            variant="secondary"
                            className="bg-black bg-opacity-50 text-white border-gray-600 hover:bg-opacity-70"
                            onClick={() => {
                              const canvas = canvasRef.current;
                              if (canvas) {
                                const link = document.createElement('a');
                                link.download = `screenshot-${Date.now()}.png`;
                                link.href = canvas.toDataURL();
                                link.click();
                              }
                            }}
                          >
                            截图
                          </Button>
                        </div>

                        {/* 视频信息覆盖层 */}
                        <div className="absolute bottom-4 left-4">
                          <div className="bg-black bg-opacity-70 text-white px-3 py-2 rounded text-sm shadow-md">
                            <div className="flex items-center gap-4">
                              <span className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-green-400 rounded-full" />
                                会话: {currentSession.id.slice(0, 8)}...
                              </span>
                              <span>1280x720</span>
                              <span>{new Date().toLocaleTimeString()}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 多模态分析 - 只在有视频会话时显示 */}
            {currentSession && (
              <div className="space-y-4">
                {/* 步骤指示 */}
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      2
                    </div>
                    <h3 className="font-medium text-blue-900">
                      视频流已打开，现在可以进行智能分析
                    </h3>
                  </div>
                  <p className="text-sm text-blue-700 mt-2 ml-8">
                    请输入分析提示词或选择预设提示词，然后点击"开始分析"按钮
                  </p>
                </div>

                <MultimodalAnalysis
                  sessionId={currentSessionId}
                  audioEnabled={currentSession.audioEnabled || false}
                  onAnalyze={handleMultimodalAnalysis}
                  disabled={false}
                  loading={isLoading}
                />
              </div>
            )}

            {/* 分析结果 - 只在有分析结果时显示 */}
            {analysisResults.length > 0 && (
              <div className="space-y-4">
                {/* 步骤指示 */}
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      3
                    </div>
                    <h3 className="font-medium text-green-900">
                      分析结果
                    </h3>
                  </div>
                  <p className="text-sm text-green-700 mt-2 ml-8">
                    大模型实时输出的分析结果如下
                  </p>
                </div>

                <AnalysisResults
                  results={analysisResults}
                  onClear={clearAnalysisResults}
                />
              </div>
            )}
          </div>

          {/* 右侧列 */}
          <div className="space-y-6">
            {/* 模型选择 */}
            <ModelSelector
              models={availableModels}
              selectedModel={selectedModel}
              onModelSelect={setSelectedModel}
            />

            {/* 音频控制 */}
            {currentSession?.audioEnabled && currentAudioStatus && (
              <AudioControls
                sessionId={currentSession.id}
                audioStatus={currentAudioStatus}
                onStartTranscription={handleStartTranscription}
                onStopTranscription={handleStopTranscription}
                onLanguageChange={() => {}} // 语言变更在开始转录时处理
              />
            )}

            {/* 转录历史 */}
            {currentSession?.audioEnabled && (
              <TranscriptionHistory
                history={currentTranscriptionHistory}
                onClear={() => currentSessionId && clearTranscriptionHistory(currentSessionId)}
              />
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
