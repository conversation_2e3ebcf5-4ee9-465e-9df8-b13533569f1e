"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AudioControls__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AudioControls */ \"(app-pages-browser)/./src/components/AudioControls.tsx\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ModelSelector */ \"(app-pages-browser)/./src/components/ModelSelector.tsx\");\n/* harmony import */ var _components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TranscriptionHistory */ \"(app-pages-browser)/./src/components/TranscriptionHistory.tsx\");\n/* harmony import */ var _components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AnalysisResults */ \"(app-pages-browser)/./src/components/AnalysisResults.tsx\");\n/* harmony import */ var _components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MultimodalAnalysis */ \"(app-pages-browser)/./src/components/MultimodalAnalysis.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/store/useAppStore */ \"(app-pages-browser)/./src/store/useAppStore.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _services_websocket__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/websocket */ \"(app-pages-browser)/./src/services/websocket.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    // Canvas引用用于视频显示\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 视频帧请求定时器\n    const frameRequestInterval = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 状态管理\n    const { videoSessions, currentSessionId, audioStatus, transcriptionHistory, availableModels, selectedModel, analysisResults, isLoading, error, wsConnected, addVideoSession, removeVideoSession, setCurrentSession, updateVideoFrame, setAudioStatus, addTranscriptionResult, clearTranscriptionHistory, setAvailableModels, setSelectedModel, addAnalysisResult, clearAnalysisResults, setLoading, setError, setWsConnected, getCurrentSession, getAudioStatus, getTranscriptionHistory } = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore)();\n    // 本地状态\n    const [rtspUrl, setRtspUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rtsp://*************:554/12');\n    const [audioEnabled, setAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentSession = getCurrentSession();\n    const currentAudioStatus = currentSessionId ? getAudioStatus(currentSessionId) : null;\n    // 调试信息\n    console.log('当前状态:', {\n        currentSessionId,\n        currentSession,\n        videoSessions,\n        hasCurrentSession: !!currentSession\n    });\n    const currentTranscriptionHistory = currentSessionId ? getTranscriptionHistory(currentSessionId) : [];\n    // WebSocket 消息处理\n    const handleWebSocketMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[handleWebSocketMessage]\": (message)=>{\n            if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isTranscriptionMessage)(message)) {\n                if (message.sessionId) {\n                    addTranscriptionResult(message.sessionId, message.result);\n                }\n            } else if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isMultimodalAnalysisMessage)(message)) {\n                addAnalysisResult(message.result);\n            } else if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isVideoFrameMessage)(message)) {\n                var _message_frameData;\n                // 处理视频帧数据\n                console.log('收到视频帧消息:', {\n                    sessionId: message.session_id,\n                    frameDataLength: ((_message_frameData = message.frameData) === null || _message_frameData === void 0 ? void 0 : _message_frameData.length) || 0,\n                    currentSessionId\n                });\n                if (currentSessionId && message.frameData) {\n                    console.log('更新视频帧数据');\n                    updateVideoFrame(currentSessionId, message.frameData);\n                } else {\n                    console.warn('无法更新视频帧:', {\n                        currentSessionId,\n                        hasFrameData: !!message.frameData\n                    });\n                }\n            }\n        }\n    }[\"HomePage.useCallback[handleWebSocketMessage]\"], [\n        addTranscriptionResult,\n        addAnalysisResult,\n        updateVideoFrame,\n        currentSessionId\n    ]);\n    // 请求视频帧\n    const requestVideoFrame = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[requestVideoFrame]\": ()=>{\n            if (_services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.getConnectionStatus() === 'connected' && currentSessionId) {\n                _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.send({\n                    type: 'request_frame',\n                    session_id: currentSessionId,\n                    timestamp: new Date().toISOString()\n                });\n            }\n        }\n    }[\"HomePage.useCallback[requestVideoFrame]\"], [\n        currentSessionId\n    ]);\n    // 启动帧请求定时器\n    const startFrameRequesting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[startFrameRequesting]\": (sessionId)=>{\n            if (frameRequestInterval.current) {\n                clearInterval(frameRequestInterval.current);\n            }\n            // 立即请求第一帧\n            if (_services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.getConnectionStatus() === 'connected') {\n                _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.send({\n                    type: 'request_frame',\n                    session_id: sessionId,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            // 设置定时器每100毫秒请求一次帧\n            frameRequestInterval.current = setInterval({\n                \"HomePage.useCallback[startFrameRequesting]\": ()=>{\n                    if (_services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.getConnectionStatus() === 'connected' && currentSessionId === sessionId) {\n                        requestVideoFrame();\n                    } else {\n                        stopFrameRequesting();\n                    }\n                }\n            }[\"HomePage.useCallback[startFrameRequesting]\"], 100);\n            console.log('启动视频帧请求定时器 (100ms)');\n        }\n    }[\"HomePage.useCallback[startFrameRequesting]\"], [\n        requestVideoFrame,\n        currentSessionId\n    ]);\n    // 停止帧请求定时器\n    const stopFrameRequesting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[stopFrameRequesting]\": ()=>{\n            if (frameRequestInterval.current) {\n                clearInterval(frameRequestInterval.current);\n                frameRequestInterval.current = null;\n                console.log('停止视频帧请求定时器');\n            }\n        }\n    }[\"HomePage.useCallback[stopFrameRequesting]\"], []);\n    // 视频帧渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            if (!currentSession || !canvasRef.current) return;\n            const canvas = canvasRef.current;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            let animationId;\n            let lastFrameData = null;\n            const drawFrame = {\n                \"HomePage.useEffect.drawFrame\": ()=>{\n                    // 如果有新的视频帧数据，绘制真实视频帧\n                    if (currentSession.currentFrame && currentSession.currentFrame !== lastFrameData) {\n                        lastFrameData = currentSession.currentFrame;\n                        const img = new Image();\n                        img.onload = ({\n                            \"HomePage.useEffect.drawFrame\": ()=>{\n                                // 清除画布\n                                ctx.clearRect(0, 0, canvas.width, canvas.height);\n                                // 计算保持宽高比的绘制尺寸\n                                const aspectRatio = img.width / img.height;\n                                const canvasAspectRatio = canvas.width / canvas.height;\n                                let drawWidth, drawHeight, drawX, drawY;\n                                if (aspectRatio > canvasAspectRatio) {\n                                    // 图片更宽，以宽度为准\n                                    drawWidth = canvas.width;\n                                    drawHeight = canvas.width / aspectRatio;\n                                    drawX = 0;\n                                    drawY = (canvas.height - drawHeight) / 2;\n                                } else {\n                                    // 图片更高，以高度为准\n                                    drawHeight = canvas.height;\n                                    drawWidth = canvas.height * aspectRatio;\n                                    drawX = (canvas.width - drawWidth) / 2;\n                                    drawY = 0;\n                                }\n                                // 绘制视频帧\n                                ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);\n                                // 添加状态信息覆盖层\n                                drawStatusOverlay(ctx, canvas);\n                            }\n                        })[\"HomePage.useEffect.drawFrame\"];\n                        // 设置图片源（base64数据）\n                        img.src = \"data:image/jpeg;base64,\".concat(currentSession.currentFrame);\n                    } else if (!currentSession.currentFrame) {\n                        // 如果没有视频帧数据，显示等待状态\n                        drawWaitingState(ctx, canvas);\n                    }\n                    animationId = requestAnimationFrame(drawFrame);\n                }\n            }[\"HomePage.useEffect.drawFrame\"];\n            // 绘制状态信息覆盖层\n            const drawStatusOverlay = {\n                \"HomePage.useEffect.drawStatusOverlay\": (ctx, canvas)=>{\n                    const time = Date.now() / 1000;\n                    // 右上角状态信息\n                    const padding = 10;\n                    const boxWidth = 200;\n                    const boxHeight = 60;\n                    // 半透明背景\n                    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';\n                    ctx.fillRect(canvas.width - boxWidth - padding, padding, boxWidth, boxHeight);\n                    // 边框\n                    ctx.strokeStyle = 'rgba(34, 197, 94, 0.8)';\n                    ctx.lineWidth = 1;\n                    ctx.strokeRect(canvas.width - boxWidth - padding, padding, boxWidth, boxHeight);\n                    // LIVE 指示器\n                    const pulse = (Math.sin(time * 3) + 1) / 2;\n                    ctx.fillStyle = \"rgba(255, 0, 0, \".concat(0.5 + pulse * 0.5, \")\");\n                    ctx.beginPath();\n                    ctx.arc(canvas.width - boxWidth - padding + 15, padding + 20, 4, 0, Math.PI * 2);\n                    ctx.fill();\n                    // 文本信息\n                    ctx.fillStyle = '#ffffff';\n                    ctx.font = 'bold 12px Arial';\n                    ctx.textAlign = 'left';\n                    ctx.fillText('● LIVE', canvas.width - boxWidth - padding + 25, padding + 25);\n                    ctx.font = '10px Arial';\n                    ctx.fillText(\"会话: \".concat(currentSession.id.slice(0, 8), \"...\"), canvas.width - boxWidth - padding + 10, padding + 45);\n                }\n            }[\"HomePage.useEffect.drawStatusOverlay\"];\n            // 绘制等待状态\n            const drawWaitingState = {\n                \"HomePage.useEffect.drawWaitingState\": (ctx, canvas)=>{\n                    const time = Date.now() / 1000;\n                    // 深色背景\n                    ctx.fillStyle = '#1a1a1a';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    // 中心等待信息\n                    const centerX = canvas.width / 2;\n                    const centerY = canvas.height / 2;\n                    // 旋转的加载指示器\n                    ctx.save();\n                    ctx.translate(centerX, centerY - 30);\n                    ctx.rotate(time * 2);\n                    ctx.strokeStyle = '#22c55e';\n                    ctx.lineWidth = 3;\n                    ctx.beginPath();\n                    ctx.arc(0, 0, 20, 0, Math.PI * 1.5);\n                    ctx.stroke();\n                    ctx.restore();\n                    // 等待文本\n                    ctx.fillStyle = '#ffffff';\n                    ctx.font = 'bold 16px Arial';\n                    ctx.textAlign = 'center';\n                    ctx.fillText('等待视频流数据...', centerX, centerY + 20);\n                    ctx.font = '12px Arial';\n                    ctx.fillStyle = '#888888';\n                    ctx.fillText('请确保RTSP地址正确且网络连接正常', centerX, centerY + 45);\n                }\n            }[\"HomePage.useEffect.drawWaitingState\"];\n            drawFrame();\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    if (animationId) {\n                        cancelAnimationFrame(animationId);\n                    }\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        currentSession\n    ]);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // 连接 WebSocket\n            _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.connect();\n            // 添加消息处理器\n            const removeMessageHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addMessageHandler(handleWebSocketMessage);\n            // 添加连接状态处理器\n            const removeConnectionHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addConnectionHandler({\n                \"HomePage.useEffect.removeConnectionHandler\": (status)=>{\n                    setWsConnected(status === 'connected');\n                }\n            }[\"HomePage.useEffect.removeConnectionHandler\"]);\n            // 加载可用模型\n            loadModels();\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    removeMessageHandler();\n                    removeConnectionHandler();\n                    stopFrameRequesting(); // 清理帧请求定时器\n                    _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.disconnect();\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        handleWebSocketMessage,\n        setWsConnected,\n        stopFrameRequesting\n    ]);\n    // 加载模型列表\n    const loadModels = async ()=>{\n        try {\n            const models = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.getAvailableModels();\n            // 确保 models 是数组\n            const modelArray = Array.isArray(models) ? models : [];\n            setAvailableModels(modelArray);\n            // 自动选择第一个可用的多模态模型\n            const multimodalModel = modelArray.find((m)=>m.type === 'multimodal' && m.isAvailable);\n            if (multimodalModel && !selectedModel) {\n                setSelectedModel(multimodalModel.id);\n            }\n        } catch (error) {\n            console.error('Failed to load models:', error);\n            setError('加载模型列表失败');\n            // 设置默认的模型数据以便测试\n            setAvailableModels([\n                {\n                    id: 'test-multimodal',\n                    name: '测试多模态模型',\n                    type: 'multimodal',\n                    provider: 'Test',\n                    description: '用于测试的模拟模型',\n                    isAvailable: true\n                },\n                {\n                    id: 'test-vision',\n                    name: '测试视觉模型',\n                    type: 'vision',\n                    provider: 'Test',\n                    description: '用于测试的视觉模型',\n                    isAvailable: true\n                }\n            ]);\n        }\n    };\n    // 打开视频流\n    const handleOpenVideo = async ()=>{\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.isValidRtspUrl)(rtspUrl)) {\n            setError('请输入有效的RTSP地址');\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        console.log('开始打开视频流:', rtspUrl);\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.openVideo({\n                rtsp_url: rtspUrl,\n                enable_audio: audioEnabled\n            });\n            console.log('API响应:', response);\n            if (response.success) {\n                const session = {\n                    id: response.session_id,\n                    rtspUrl,\n                    isActive: true,\n                    audioEnabled,\n                    createdAt: new Date()\n                };\n                console.log('创建会话:', session);\n                addVideoSession(session);\n                setCurrentSession(response.session_id);\n                // 设置音频状态\n                if (response.audio_status) {\n                    setAudioStatus(response.session_id, response.audio_status);\n                }\n                // 启动视频帧请求\n                startFrameRequesting(response.session_id);\n                console.log('视频流打开成功');\n            } else {\n                console.error('API返回失败:', response);\n                setError(response.message || '打开视频流失败');\n            }\n        } catch (error) {\n            console.error('API调用失败:', error);\n            setError('连接视频流失败，请检查网络和RTSP地址');\n            // 为了测试，我们可以创建一个模拟会话\n            console.log('创建模拟会话进行测试');\n            const mockSession = {\n                id: \"mock-\".concat(Date.now()),\n                rtspUrl,\n                isActive: true,\n                audioEnabled,\n                createdAt: new Date()\n            };\n            addVideoSession(mockSession);\n            setCurrentSession(mockSession.id);\n            // 即使是模拟会话也启动帧请求（用于测试）\n            startFrameRequesting(mockSession.id);\n            setError('使用模拟视频流（后端连接失败）');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 关闭视频流\n    const handleCloseVideo = async ()=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            // 停止帧请求\n            stopFrameRequesting();\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.closeVideo(currentSessionId);\n            removeVideoSession(currentSessionId);\n            setCurrentSession(null);\n        } catch (error) {\n            console.error('Failed to close video:', error);\n            setError('关闭视频流失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 开始音频转录\n    const handleStartTranscription = async (language)=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.startAudioTranscription({\n                session_id: currentSessionId,\n                language\n            });\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: true,\n                    language\n                });\n            }\n        } catch (error) {\n            setError('启动音频转录失败');\n        }\n    };\n    // 停止音频转录\n    const handleStopTranscription = async ()=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.stopAudioTranscription(currentSessionId);\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: false\n                });\n            }\n        } catch (error) {\n            setError('停止音频转录失败');\n        }\n    };\n    // 多模态分析\n    const handleMultimodalAnalysis = async (prompt, includeAudio, contextSeconds)=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.analyzeMultimodal({\n                session_id: currentSessionId,\n                prompt,\n                include_recent_audio: includeAudio,\n                audio_context_seconds: contextSeconds\n            });\n            addAnalysisResult(result);\n        } catch (error) {\n            setError('多模态分析失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"VA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"视频分析系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                        variant: wsConnected ? \"success\" : \"danger\",\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            wsConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 32\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 63\n                                            }, this),\n                                            wsConnected ? '已连接' : '未连接'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        size: \"sm\",\n                                        variant: \"ghost\",\n                                        icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 523,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 522,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-5 h-5 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>setError(null),\n                                className: \"ml-auto\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    children: \"视频流控制\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        !currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium text-blue-900\",\n                                                                            children: \"第一步：打开视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                                    children: '输入RTSP地址，选择是否启用音频，然后点击\"打开视频流\"按钮'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    label: \"RTSP地址\",\n                                                                    value: rtspUrl,\n                                                                    onChange: (e)=>setRtspUrl(e.target.value),\n                                                                    placeholder: \"rtsp://*************:554/12\",\n                                                                    disabled: !!currentSession\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-end gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center gap-2 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: audioEnabled,\n                                                                                onChange: (e)=>setAudioEnabled(e.target.checked),\n                                                                                disabled: !!currentSession,\n                                                                                className: \"rounded\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 608,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"启用音频处理\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: !currentSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleOpenVideo,\n                                                                loading: isLoading,\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"打开视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleCloseVideo,\n                                                                loading: isLoading,\n                                                                variant: \"danger\",\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"关闭视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                            children: \"实时视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"RTSP地址: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-mono text-xs\",\n                                                                                    children: currentSession.rtspUrl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 649,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 648,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative bg-black rounded-lg overflow-hidden aspect-video border-2 border-gray-300 shadow-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                                                            ref: canvasRef,\n                                                                            width: 1280,\n                                                                            height: 720,\n                                                                            className: \"w-full h-full object-contain\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"success\",\n                                                                                className: \"bg-green-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 668,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"实时播放\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 664,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 663,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        currentSession.audioEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 right-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"primary\",\n                                                                                className: \"bg-blue-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 680,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"音频已启用\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 676,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 675,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 right-4 flex gap-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                                size: \"sm\",\n                                                                                variant: \"secondary\",\n                                                                                className: \"bg-black bg-opacity-50 text-white border-gray-600 hover:bg-opacity-70\",\n                                                                                onClick: ()=>{\n                                                                                    const canvas = canvasRef.current;\n                                                                                    if (canvas) {\n                                                                                        const link = document.createElement('a');\n                                                                                        link.download = \"screenshot-\".concat(Date.now(), \".png\");\n                                                                                        link.href = canvas.toDataURL();\n                                                                                        link.click();\n                                                                                    }\n                                                                                },\n                                                                                children: \"截图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 688,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 687,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-black bg-opacity-70 text-white px-3 py-2 rounded text-sm shadow-md\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"flex items-center gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                                    lineNumber: 711,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                \"会话: \",\n                                                                                                currentSession.id.slice(0, 8),\n                                                                                                \"...\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 710,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"1280x720\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 714,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: new Date().toLocaleTimeString()\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 715,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, this),\n                                    currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-blue-900\",\n                                                                children: \"视频流已打开，现在可以进行智能分析\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                        children: '请输入分析提示词或选择预设提示词，然后点击\"开始分析\"按钮'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__.MultimodalAnalysis, {\n                                                sessionId: currentSessionId,\n                                                audioEnabled: currentSession.audioEnabled || false,\n                                                onAnalyze: handleMultimodalAnalysis,\n                                                disabled: false,\n                                                loading: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 15\n                                    }, this),\n                                    analysisResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-green-50 border border-green-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-green-900\",\n                                                                children: \"分析结果\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700 mt-2 ml-8\",\n                                                        children: \"大模型实时输出的分析结果如下\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__.AnalysisResults, {\n                                                results: analysisResults,\n                                                onClear: clearAnalysisResults\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 756,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__.ModelSelector, {\n                                        models: availableModels,\n                                        selectedModel: selectedModel,\n                                        onModelSelect: setSelectedModel\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 13\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && currentAudioStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioControls__WEBPACK_IMPORTED_MODULE_2__.AudioControls, {\n                                        sessionId: currentSession.id,\n                                        audioStatus: currentAudioStatus,\n                                        onStartTranscription: handleStartTranscription,\n                                        onStopTranscription: handleStopTranscription,\n                                        onLanguageChange: ()=>{}\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__.TranscriptionHistory, {\n                                        history: currentTranscriptionHistory,\n                                        onClear: ()=>currentSessionId && clearTranscriptionHistory(currentSessionId)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 781,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 553,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n        lineNumber: 520,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"8ZVF77PnqDsdg6fQAfWrXVsxCQI=\", false, function() {\n    return [\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});