#!/usr/bin/env python3
"""
RTSP转码器 - 将RTSP流转为HTTP MJPEG流，解决H.264解码问题
用法: python rtsp_transcoder.py rtsp://摄像头IP/地址 [端口号]
"""

import sys
import os
import time
import signal
import subprocess
import argparse
import logging
import socket
from threading import Thread
import http.server
import socketserver
import threading
from queue import Queue, Empty
import shutil
import tempfile

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("RTSP转码器")

def find_free_port():
    """查找可用的端口"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        return s.getsockname()[1]

class MJPEGHandler(http.server.BaseHTTPRequestHandler):
    """MJPEG HTTP处理器"""
    
    frames_queue = Queue(maxsize=10)
    active = True
    
    def do_HEAD(self):
        """处理HEAD请求"""
        if self.path == '/stream.mjpeg':
            self.send_response(200)
            self.send_header('Content-type', 'multipart/x-mixed-replace; boundary=--jpgboundary')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_GET(self):
        """处理GET请求，发送MJPEG流"""
        if self.path == '/stream.mjpeg':
            self.send_response(200)
            self.send_header('Content-type', 'multipart/x-mixed-replace; boundary=--jpgboundary')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
            
            try:
                while MJPEGHandler.active:
                    try:
                        # 尝试从队列获取帧，如果30秒内没有新帧则超时
                        frame = MJPEGHandler.frames_queue.get(timeout=30)
                        
                        # 发送帧
                        self.wfile.write(b"--jpgboundary\r\n")
                        self.wfile.write(b"Content-Type: image/jpeg\r\n")
                        self.wfile.write(f"Content-Length: {len(frame)}\r\n\r\n".encode())
                        self.wfile.write(frame)
                        self.wfile.write(b"\r\n")
                    except Empty:
                        # 队列超时，发送空帧保持连接
                        logger.warning("30秒内未收到新帧，可能连接有问题")
                        continue
                    except Exception as e:
                        logger.error(f"发送帧时出错: {e}")
                        break
            except ConnectionError:
                logger.info("客户端已断开连接")
            except Exception as e:
                logger.error(f"流处理错误: {e}")
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'404 - Not Found')

    def log_message(self, format, *args):
        """覆盖日志方法，减少输出"""
        return

class RTSPTranscoder:
    """RTSP转HTTP MJPEG转码器"""
    
    def __init__(self, rtsp_url, http_port=None):
        self.rtsp_url = rtsp_url
        self.http_port = http_port or find_free_port()
        self.process = None
        self.is_running = False
        self.http_url = f"http://localhost:{self.http_port}/stream.mjpeg"
        self.temp_dir = tempfile.mkdtemp(prefix="rtsp_transcoder_")
        self.http_server = None
        self.server_thread = None
        
    def start_http_server(self):
        """启动HTTP服务器"""
        try:
            # 创建简单的HTTP服务器
            handler = MJPEGHandler
            self.http_server = socketserver.ThreadingTCPServer(("0.0.0.0", self.http_port), handler)
            
            # 在单独的线程中运行HTTP服务器
            self.server_thread = threading.Thread(target=self.http_server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            logger.info(f"HTTP服务器已启动在端口 {self.http_port}")
            return True
        except Exception as e:
            logger.error(f"启动HTTP服务器失败: {e}")
            return False
    
    def start(self):
        """启动转码器"""
        if self.is_running:
            logger.warning("转码器已在运行")
            return self.http_url
        
        # 启动HTTP服务器
        if not self.start_http_server():
            return None
            
        # 构建FFmpeg命令 - 使用更健壮的参数处理损坏数据
        latest_frame_path = os.path.join(self.temp_dir, 'latest_frame.jpg')
        cmd = [
            'ffmpeg',
            '-hide_banner',                    # 隐藏版权信息
            '-loglevel', 'error',              # 只显示错误
            '-rtsp_transport', 'tcp',          # 使用TCP，更可靠
            '-analyzeduration', '2000000',     # 增加分析时间
            '-probesize', '1000000',           # 增加探测大小
            '-fflags', '+genpts+discardcorrupt+igndts',  # 处理损坏数据
            '-err_detect', 'ignore_err',       # 忽略错误
            '-i', self.rtsp_url,               # 输入RTSP流
            '-vf', 'scale=640:480',            # 缩放到较小分辨率
            '-r', '15',                        # 降低帧率为15fps
            '-q:v', '8',                       # 中等质量，减小文件大小
            '-an',                             # 不处理音频
            '-f', 'image2',                    # 图像序列格式
            '-update', '1',                    # 更新同一文件
            latest_frame_path                  # 输出到固定文件
        ]
        
        cmd_str = ' '.join(cmd)
        logger.info(f"启动RTSP转码器: {cmd_str}")
        
        try:
            # 启动FFmpeg进程
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 启动错误监控线程
            self.error_thread = Thread(target=self._monitor_errors)
            self.error_thread.daemon = True
            self.error_thread.start()
            
            # 启动帧读取线程
            self.frame_thread = Thread(target=self._process_frames, args=(latest_frame_path,))
            self.frame_thread.daemon = True
            self.frame_thread.start()
            
            self.is_running = True
            logger.info(f"转码器启动成功: {self.rtsp_url} -> {self.http_url}")
            
            # 等待服务启动
            time.sleep(2)
            return self.http_url
            
        except Exception as e:
            logger.error(f"启动转码器失败: {e}")
            if self.process:
                self.process.terminate()
                self.process = None
            return None
    
    def _process_frames(self, frame_file_path):
        """处理生成的帧并放入队列"""
        frame_count = 0
        last_frame_time = time.time()
        last_log_time = time.time()
        last_mtime = 0

        logger.info(f"开始处理帧，监视文件: {frame_file_path}")

        while self.is_running:
            try:
                # 检查文件是否存在且已更新
                if os.path.exists(frame_file_path):
                    current_mtime = os.path.getmtime(frame_file_path)

                    # 如果文件已更新
                    if current_mtime > last_mtime:
                        last_mtime = current_mtime

                        try:
                            # 等待一小段时间确保文件写入完成
                            time.sleep(0.01)

                            # 读取帧数据
                            with open(frame_file_path, 'rb') as f:
                                frame_data = f.read()

                            # 确保帧数据有效
                            if len(frame_data) > 100:  # 确保不是空文件
                                # 如果队列已满，先移除旧帧
                                if MJPEGHandler.frames_queue.full():
                                    try:
                                        MJPEGHandler.frames_queue.get_nowait()
                                    except Empty:
                                        pass

                                MJPEGHandler.frames_queue.put(frame_data)
                                frame_count += 1

                                # 每100帧记录一次
                                if frame_count % 100 == 0:
                                    logger.info(f"已处理 {frame_count} 帧")

                                # 更新最后一帧时间
                                last_frame_time = time.time()
                        except Exception as e:
                            logger.error(f"读取帧文件失败: {e}")
                else:
                    # 如果10秒没有新帧，记录警告
                    if time.time() - last_frame_time > 10:
                        logger.warning("10秒内未收到新帧，检查RTSP源")
                        last_frame_time = time.time()  # 重置时间，避免重复警告

                # 休眠一小段时间
                time.sleep(0.05)
                
            except Exception as e:
                logger.error(f"处理帧时出错: {e}")
                time.sleep(1)  # 错误后等待更长时间
    
    def _monitor_errors(self):
        """监控FFmpeg错误输出"""
        while self.is_running and self.process:
            line = self.process.stderr.readline()
            if not line:
                break
                
            line = line.decode('utf-8', errors='ignore').strip()
            if line:
                logger.error(f"FFmpeg错误: {line}")
                
        logger.info("错误监控线程结束")
    
    def stop(self):
        """停止转码器"""
        self.is_running = False
        MJPEGHandler.active = False
        
        if self.process:
            logger.info("正在停止转码器...")
            try:
                self.process.terminate()
                self.process.wait(timeout=3)
                logger.info("转码器已正常停止")
            except subprocess.TimeoutExpired:
                logger.warning("转码器未能正常停止，强制终止")
                self.process.kill()
            except Exception as e:
                logger.error(f"停止转码器时出错: {e}")
            finally:
                self.process = None
        
        # 停止HTTP服务器
        if self.http_server:
            try:
                self.http_server.shutdown()
                self.http_server.server_close()
                logger.info("HTTP服务器已停止")
            except Exception as e:
                logger.error(f"停止HTTP服务器时出错: {e}")
        
        # 清理临时目录
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"临时目录已清理: {self.temp_dir}")
        except Exception as e:
            logger.error(f"清理临时目录时出错: {e}")
        
        logger.info("转码器已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RTSP流转HTTP MJPEG转码器')
    parser.add_argument('rtsp_url', help='RTSP流地址')
    parser.add_argument('--port', '-p', type=int, help='HTTP服务器端口 (默认自动选择)')
    parser.add_argument('--quiet', '-q', action='store_true', help='静默模式')
    
    args = parser.parse_args()
    
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    transcoder = RTSPTranscoder(args.rtsp_url, args.port)
    
    # 注册信号处理器
    def signal_handler(sig, frame):
        logger.info("接收到终止信号，正在停止...")
        transcoder.stop()
        sys.exit(0)
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动转码器
    http_url = transcoder.start()
    
    if http_url:
        logger.info(f"转码服务已启动！请使用以下URL替代原RTSP地址:")
        logger.info(f"HTTP URL: {http_url}")
        
        print(f"\n转码服务已成功启动！")
        print(f"在应用中使用此URL: {http_url}")
        print("按 Ctrl+C 停止服务\n")
        
        # 保持程序运行
        try:
            while transcoder.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            transcoder.stop()
    else:
        logger.error("转码器启动失败")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 