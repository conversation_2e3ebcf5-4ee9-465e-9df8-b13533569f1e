"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   LANGUAGE_OPTIONS: () => (/* binding */ LANGUAGE_OPTIONS),\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/polyfills/process.js\");\nconst config = {\n    apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000',\n    wsUrl: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws/frontend-client',\n    maxTranscriptionHistory: 50,\n    maxAnalysisResults: 20,\n    audioContextSeconds: 30,\n    reconnectInterval: 3000,\n    maxReconnectAttempts: 5\n};\nconst API_ENDPOINTS = {\n    // 视频相关\n    VIDEO_OPEN: '/video/open',\n    VIDEO_CLOSE: '/video/close',\n    VIDEO_ANALYZE: '/video/analyze',\n    // 音频相关\n    AUDIO_TRANSCRIPTION_START: '/audio/transcription/start',\n    AUDIO_TRANSCRIPTION_STOP: '/audio/transcription/stop',\n    AUDIO_STATUS: '/audio/status',\n    AUDIO_HISTORY: '/audio/transcription/history',\n    // 多模态分析\n    MULTIMODAL_ANALYZE: '/multimodal/analyze',\n    // 模型相关\n    MODELS_LIST: '/models',\n    MODEL_SELECT: '/model/select'\n};\nconst LANGUAGE_OPTIONS = [\n    {\n        value: 'auto',\n        label: '自动检测',\n        flag: '🌐'\n    },\n    {\n        value: 'zh',\n        label: '中文',\n        flag: '🇨🇳'\n    },\n    {\n        value: 'en',\n        label: 'English',\n        flag: '🇺🇸'\n    },\n    {\n        value: 'yue',\n        label: '粤语',\n        flag: '🇭🇰'\n    },\n    {\n        value: 'ja',\n        label: '日本語',\n        flag: '🇯🇵'\n    },\n    {\n        value: 'ko',\n        label: '한국어',\n        flag: '🇰🇷'\n    },\n    {\n        value: 'es',\n        label: 'Español',\n        flag: '🇪🇸'\n    },\n    {\n        value: 'fr',\n        label: 'Français',\n        flag: '🇫🇷'\n    },\n    {\n        value: 'de',\n        label: 'Deutsch',\n        flag: '🇩🇪'\n    },\n    {\n        value: 'it',\n        label: 'Italiano',\n        flag: '🇮🇹'\n    },\n    {\n        value: 'pt',\n        label: 'Português',\n        flag: '🇵🇹'\n    },\n    {\n        value: 'ru',\n        label: 'Русский',\n        flag: '🇷🇺'\n    },\n    {\n        value: 'ar',\n        label: 'العربية',\n        flag: '🇸🇦'\n    },\n    {\n        value: 'hi',\n        label: 'हिन्दी',\n        flag: '🇮🇳'\n    },\n    {\n        value: 'th',\n        label: 'ไทย',\n        flag: '🇹🇭'\n    },\n    {\n        value: 'vi',\n        label: 'Tiếng Việt',\n        flag: '🇻🇳'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});