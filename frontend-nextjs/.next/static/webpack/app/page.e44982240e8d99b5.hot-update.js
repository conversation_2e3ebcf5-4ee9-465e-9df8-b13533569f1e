"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/services/websocket.ts":
/*!***********************************!*\
  !*** ./src/services/websocket.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFrameAnalysisMessage: () => (/* binding */ isFrameAnalysisMessage),\n/* harmony export */   isMultimodalAnalysisMessage: () => (/* binding */ isMultimodalAnalysisMessage),\n/* harmony export */   isTranscriptionMessage: () => (/* binding */ isTranscriptionMessage),\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket),\n/* harmony export */   wsService: () => (/* binding */ wsService)\n/* harmony export */ });\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n\nclass WebSocketService {\n    connect() {\n        var _this_ws;\n        if (((_this_ws = this.ws) === null || _this_ws === void 0 ? void 0 : _this_ws.readyState) === WebSocket.OPEN) {\n            return;\n        }\n        this.isManualClose = false;\n        this.notifyConnectionHandlers('connecting');\n        try {\n            this.ws = new WebSocket(this.url);\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('WebSocket connection failed:', error);\n            this.notifyConnectionHandlers('error');\n            this.scheduleReconnect();\n        }\n    }\n    disconnect() {\n        this.isManualClose = true;\n        this.clearReconnectTimer();\n        if (this.ws) {\n            this.ws.close();\n            this.ws = null;\n        }\n        this.notifyConnectionHandlers('disconnected');\n    }\n    send(message) {\n        var _this_ws;\n        if (((_this_ws = this.ws) === null || _this_ws === void 0 ? void 0 : _this_ws.readyState) === WebSocket.OPEN) {\n            try {\n                this.ws.send(JSON.stringify(message));\n                return true;\n            } catch (error) {\n                console.error('Failed to send WebSocket message:', error);\n                return false;\n            }\n        }\n        return false;\n    }\n    addMessageHandler(handler) {\n        this.messageHandlers.add(handler);\n        return ()=>this.messageHandlers.delete(handler);\n    }\n    addConnectionHandler(handler) {\n        this.connectionHandlers.add(handler);\n        return ()=>this.connectionHandlers.delete(handler);\n    }\n    getConnectionStatus() {\n        if (!this.ws) return 'disconnected';\n        switch(this.ws.readyState){\n            case WebSocket.CONNECTING:\n                return 'connecting';\n            case WebSocket.OPEN:\n                return 'connected';\n            case WebSocket.CLOSING:\n            case WebSocket.CLOSED:\n                return 'disconnected';\n            default:\n                return 'error';\n        }\n    }\n    setupEventListeners() {\n        if (!this.ws) return;\n        this.ws.onopen = ()=>{\n            console.log('WebSocket connected');\n            this.reconnectAttempts = 0;\n            this.clearReconnectTimer();\n            this.notifyConnectionHandlers('connected');\n        };\n        this.ws.onmessage = (event)=>{\n            try {\n                const data = JSON.parse(event.data);\n                const message = {\n                    ...data,\n                    timestamp: new Date(data.timestamp || Date.now())\n                };\n                this.notifyMessageHandlers(message);\n            } catch (error) {\n                console.error('Failed to parse WebSocket message:', error);\n            }\n        };\n        this.ws.onclose = (event)=>{\n            console.log('WebSocket disconnected:', event.code, event.reason);\n            this.ws = null;\n            if (!this.isManualClose) {\n                this.notifyConnectionHandlers('disconnected');\n                this.scheduleReconnect();\n            }\n        };\n        this.ws.onerror = (error)=>{\n            console.warn('WebSocket connection failed - backend server may not be running');\n            this.notifyConnectionHandlers('error');\n        };\n    }\n    scheduleReconnect() {\n        if (this.isManualClose || this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.log('Max reconnect attempts reached or manual close');\n            return;\n        }\n        this.clearReconnectTimer();\n        this.reconnectAttempts++;\n        console.log(\"Scheduling reconnect attempt \".concat(this.reconnectAttempts, \"/\").concat(this.maxReconnectAttempts));\n        this.reconnectTimer = setTimeout(()=>{\n            this.connect();\n        }, this.reconnectInterval);\n    }\n    clearReconnectTimer() {\n        if (this.reconnectTimer) {\n            clearTimeout(this.reconnectTimer);\n            this.reconnectTimer = null;\n        }\n    }\n    notifyMessageHandlers(message) {\n        this.messageHandlers.forEach((handler)=>{\n            try {\n                handler(message);\n            } catch (error) {\n                console.error('Error in message handler:', error);\n            }\n        });\n    }\n    notifyConnectionHandlers(status) {\n        this.connectionHandlers.forEach((handler)=>{\n            try {\n                handler(status);\n            } catch (error) {\n                console.error('Error in connection handler:', error);\n            }\n        });\n    }\n    constructor(){\n        this.ws = null;\n        this.reconnectAttempts = 0;\n        this.reconnectTimer = null;\n        this.messageHandlers = new Set();\n        this.connectionHandlers = new Set();\n        this.isManualClose = false;\n        this.url = _lib_config__WEBPACK_IMPORTED_MODULE_0__.config.wsUrl;\n        this.maxReconnectAttempts = _lib_config__WEBPACK_IMPORTED_MODULE_0__.config.maxReconnectAttempts;\n        this.reconnectInterval = _lib_config__WEBPACK_IMPORTED_MODULE_0__.config.reconnectInterval;\n    }\n}\n// 创建单例实例\nconst wsService = new WebSocketService();\n// 类型守卫函数\nfunction isFrameAnalysisMessage(message) {\n    return message.type === 'frame_analysis';\n}\nfunction isTranscriptionMessage(message) {\n    return message.type === 'transcription_result';\n}\nfunction isMultimodalAnalysisMessage(message) {\n    return message.type === 'multimodal_analysis_result';\n}\n// Hook for using WebSocket in components\nfunction useWebSocket() {\n    return {\n        connect: ()=>wsService.connect(),\n        disconnect: ()=>wsService.disconnect(),\n        send: (message)=>wsService.send(message),\n        addMessageHandler: (handler)=>wsService.addMessageHandler(handler),\n        addConnectionHandler: (handler)=>wsService.addConnectionHandler(handler),\n        getConnectionStatus: ()=>wsService.getConnectionStatus()\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/websocket.ts\n"));

/***/ })

});