"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_VideoPlayer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/VideoPlayer */ \"(app-pages-browser)/./src/components/VideoPlayer.tsx\");\n/* harmony import */ var _components_AudioControls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AudioControls */ \"(app-pages-browser)/./src/components/AudioControls.tsx\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ModelSelector */ \"(app-pages-browser)/./src/components/ModelSelector.tsx\");\n/* harmony import */ var _components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/TranscriptionHistory */ \"(app-pages-browser)/./src/components/TranscriptionHistory.tsx\");\n/* harmony import */ var _components_AnalysisResults__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AnalysisResults */ \"(app-pages-browser)/./src/components/AnalysisResults.tsx\");\n/* harmony import */ var _components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/MultimodalAnalysis */ \"(app-pages-browser)/./src/components/MultimodalAnalysis.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _store_useAppStore__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/store/useAppStore */ \"(app-pages-browser)/./src/store/useAppStore.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _services_websocket__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/websocket */ \"(app-pages-browser)/./src/services/websocket.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    // 状态管理\n    const { videoSessions, currentSessionId, audioStatus, transcriptionHistory, availableModels, selectedModel, analysisResults, isLoading, error, wsConnected, addVideoSession, removeVideoSession, setCurrentSession, setAudioStatus, addTranscriptionResult, clearTranscriptionHistory, setAvailableModels, setSelectedModel, addAnalysisResult, clearAnalysisResults, setLoading, setError, setWsConnected, getCurrentSession, getAudioStatus, getTranscriptionHistory } = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_12__.useAppStore)();\n    // 本地状态\n    const [rtspUrl, setRtspUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rtsp://*************:554/12');\n    const [audioEnabled, setAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentSession = getCurrentSession();\n    const currentAudioStatus = currentSessionId ? getAudioStatus(currentSessionId) : null;\n    const currentTranscriptionHistory = currentSessionId ? getTranscriptionHistory(currentSessionId) : [];\n    // WebSocket 消息处理\n    const handleWebSocketMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[handleWebSocketMessage]\": (message)=>{\n            if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_14__.isTranscriptionMessage)(message)) {\n                if (message.sessionId) {\n                    addTranscriptionResult(message.sessionId, message.result);\n                }\n            } else if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_14__.isMultimodalAnalysisMessage)(message)) {\n                addAnalysisResult(message.result);\n            }\n        }\n    }[\"HomePage.useCallback[handleWebSocketMessage]\"], [\n        addTranscriptionResult,\n        addAnalysisResult\n    ]);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // 连接 WebSocket\n            _services_websocket__WEBPACK_IMPORTED_MODULE_14__.wsService.connect();\n            // 添加消息处理器\n            const removeMessageHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_14__.wsService.addMessageHandler(handleWebSocketMessage);\n            // 添加连接状态处理器\n            const removeConnectionHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_14__.wsService.addConnectionHandler({\n                \"HomePage.useEffect.removeConnectionHandler\": (status)=>{\n                    setWsConnected(status === 'connected');\n                }\n            }[\"HomePage.useEffect.removeConnectionHandler\"]);\n            // 加载可用模型\n            loadModels();\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    removeMessageHandler();\n                    removeConnectionHandler();\n                    _services_websocket__WEBPACK_IMPORTED_MODULE_14__.wsService.disconnect();\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        handleWebSocketMessage,\n        setWsConnected\n    ]);\n    // 加载模型列表\n    const loadModels = async ()=>{\n        try {\n            const models = await _services_api__WEBPACK_IMPORTED_MODULE_13__.apiService.getAvailableModels();\n            // 确保 models 是数组\n            const modelArray = Array.isArray(models) ? models : [];\n            setAvailableModels(modelArray);\n            // 自动选择第一个可用的多模态模型\n            const multimodalModel = modelArray.find((m)=>m.type === 'multimodal' && m.isAvailable);\n            if (multimodalModel && !selectedModel) {\n                setSelectedModel(multimodalModel.id);\n            }\n        } catch (error) {\n            console.error('Failed to load models:', error);\n            setError('加载模型列表失败');\n            // 设置默认的模型数据以便测试\n            setAvailableModels([\n                {\n                    id: 'test-multimodal',\n                    name: '测试多模态模型',\n                    type: 'multimodal',\n                    provider: 'Test',\n                    description: '用于测试的模拟模型',\n                    isAvailable: true\n                },\n                {\n                    id: 'test-vision',\n                    name: '测试视觉模型',\n                    type: 'vision',\n                    provider: 'Test',\n                    description: '用于测试的视觉模型',\n                    isAvailable: true\n                }\n            ]);\n        }\n    };\n    // 打开视频流\n    const handleOpenVideo = async ()=>{\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.isValidRtspUrl)(rtspUrl)) {\n            setError('请输入有效的RTSP地址');\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_13__.apiService.openVideo({\n                rtsp_url: rtspUrl,\n                enable_audio: audioEnabled\n            });\n            if (response.success) {\n                const session = {\n                    id: response.session_id,\n                    rtspUrl,\n                    isActive: true,\n                    audioEnabled,\n                    createdAt: new Date()\n                };\n                addVideoSession(session);\n                setCurrentSession(response.session_id);\n                // 设置音频状态\n                if (response.audio_status) {\n                    setAudioStatus(response.session_id, response.audio_status);\n                }\n            } else {\n                setError(response.message || '打开视频流失败');\n            }\n        } catch (error) {\n            setError('连接视频流失败，请检查网络和RTSP地址');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 关闭视频流\n    const handleCloseVideo = async ()=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_13__.apiService.closeVideo(currentSessionId);\n            removeVideoSession(currentSessionId);\n            setCurrentSession(null);\n        } catch (error) {\n            console.error('Failed to close video:', error);\n            setError('关闭视频流失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 开始音频转录\n    const handleStartTranscription = async (language)=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_13__.apiService.startAudioTranscription({\n                session_id: currentSessionId,\n                language\n            });\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: true,\n                    language\n                });\n            }\n        } catch (error) {\n            setError('启动音频转录失败');\n        }\n    };\n    // 停止音频转录\n    const handleStopTranscription = async ()=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_13__.apiService.stopAudioTranscription(currentSessionId);\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: false\n                });\n            }\n        } catch (error) {\n            setError('停止音频转录失败');\n        }\n    };\n    // 多模态分析\n    const handleMultimodalAnalysis = async (prompt, includeAudio, contextSeconds)=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_13__.apiService.analyzeMultimodal({\n                session_id: currentSessionId,\n                prompt,\n                include_recent_audio: includeAudio,\n                audio_context_seconds: contextSeconds\n            });\n            addAnalysisResult(result);\n        } catch (error) {\n            setError('多模态分析失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"VA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"视频分析系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                        variant: wsConnected ? \"success\" : \"danger\",\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            wsConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 32\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 63\n                                            }, this),\n                                            wsConnected ? '已连接' : '未连接'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                        size: \"sm\",\n                                        variant: \"ghost\",\n                                        icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"w-5 h-5 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>setError(null),\n                                className: \"ml-auto\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                    children: \"视频流控制\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                                                    label: \"RTSP地址\",\n                                                                    value: rtspUrl,\n                                                                    onChange: (e)=>setRtspUrl(e.target.value),\n                                                                    placeholder: \"rtsp://*************:554/12\",\n                                                                    disabled: !!currentSession\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-end gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center gap-2 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: audioEnabled,\n                                                                                onChange: (e)=>setAudioEnabled(e.target.checked),\n                                                                                disabled: !!currentSession,\n                                                                                className: \"rounded\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"启用音频处理\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: !currentSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                onClick: handleOpenVideo,\n                                                                loading: isLoading,\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"打开视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                onClick: handleCloseVideo,\n                                                                loading: isLoading,\n                                                                variant: \"danger\",\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"关闭视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this),\n                                    currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoPlayer__WEBPACK_IMPORTED_MODULE_2__.VideoPlayer, {\n                                        sessionId: currentSession.id,\n                                        rtspUrl: currentSession.rtspUrl,\n                                        onError: setError\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_7__.MultimodalAnalysis, {\n                                        sessionId: currentSessionId,\n                                        audioEnabled: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) || false,\n                                        onAnalyze: handleMultimodalAnalysis,\n                                        disabled: !currentSession,\n                                        loading: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisResults__WEBPACK_IMPORTED_MODULE_6__.AnalysisResults, {\n                                        results: analysisResults,\n                                        onClear: clearAnalysisResults\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_4__.ModelSelector, {\n                                        models: availableModels,\n                                        selectedModel: selectedModel,\n                                        onModelSelect: setSelectedModel\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && currentAudioStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioControls__WEBPACK_IMPORTED_MODULE_3__.AudioControls, {\n                                        sessionId: currentSession.id,\n                                        audioStatus: currentAudioStatus,\n                                        onStartTranscription: handleStartTranscription,\n                                        onStopTranscription: handleStopTranscription,\n                                        onLanguageChange: ()=>{}\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_5__.TranscriptionHistory, {\n                                        history: currentTranscriptionHistory,\n                                        onClear: ()=>currentSessionId && clearTranscriptionHistory(currentSessionId)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"mYYwiKstJivynOFo4sZ3kjcaVAg=\", false, function() {\n    return [\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_12__.useAppStore\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});