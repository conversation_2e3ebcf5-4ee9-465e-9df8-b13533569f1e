"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ModelSelector.tsx":
/*!******************************************!*\
  !*** ./src/components/ModelSelector.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModelSelector: () => (/* binding */ ModelSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Select */ \"(app-pages-browser)/./src/components/ui/Select.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* __next_internal_client_entry_do_not_use__ ModelSelector auto */ \n\n\n\n\n\n\nfunction ModelSelector(param) {\n    let { models, selectedModel, onModelSelect, disabled = false } = param;\n    // 确保 models 是数组\n    const modelArray = Array.isArray(models) ? models : [];\n    const selectedModelInfo = modelArray.find((model)=>model.id === selectedModel);\n    const getModelTypeIcon = (type)=>{\n        switch(type){\n            case 'vision':\n                return '👁️';\n            case 'language':\n                return '💬';\n            case 'multimodal':\n                return '🧠';\n            default:\n                return '🤖';\n        }\n    };\n    const getModelTypeBadge = (type)=>{\n        const variants = {\n            vision: 'primary',\n            language: 'success',\n            multimodal: 'warning'\n        };\n        const labels = {\n            vision: '视觉',\n            language: '语言',\n            multimodal: '多模态'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n            variant: variants[type] || 'secondary',\n            children: labels[type] || type\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    };\n    const availableModels = modelArray.filter((model)=>model.isAvailable);\n    const unavailableModels = modelArray.filter((model)=>!model.isAvailable);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                \"模型选择\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            size: \"sm\",\n                            variant: \"ghost\",\n                            icon: _barrel_optimize_names_Brain_CheckCircle_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                            disabled: disabled,\n                            children: \"刷新\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                label: \"选择AI模型\",\n                                value: selectedModel || '',\n                                onChange: (e)=>onModelSelect(e.target.value),\n                                options: [\n                                    ...availableModels.map((model)=>({\n                                            value: model.id,\n                                            label: \"\".concat(getModelTypeIcon(model.type), \" \").concat(model.name, \" (\").concat(model.provider, \")\")\n                                        })),\n                                    ...unavailableModels.length > 0 ? [\n                                        {\n                                            value: '',\n                                            label: '--- 不可用的模型 ---',\n                                            disabled: true\n                                        },\n                                        ...unavailableModels.map((model)=>({\n                                                value: model.id,\n                                                label: \"\".concat(getModelTypeIcon(model.type), \" \").concat(model.name, \" (\").concat(model.provider, \") - 不可用\"),\n                                                disabled: true\n                                            }))\n                                    ] : []\n                                ],\n                                placeholder: \"请选择一个模型\",\n                                disabled: disabled || availableModels.length === 0\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        selectedModelInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-gray-50 rounded-lg space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: selectedModelInfo.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                getModelTypeBadge(selectedModelInfo.type),\n                                                selectedModelInfo.isAvailable ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"success\",\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"可用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"danger\",\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"不可用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-600\",\n                                                    children: \"提供商:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2\",\n                                                    children: selectedModelInfo.provider\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-600\",\n                                                    children: \"类型:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2\",\n                                                    children: selectedModelInfo.type\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                selectedModelInfo.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-600\",\n                                            children: \"描述:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-gray-700\",\n                                            children: selectedModelInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-blue-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: modelArray.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-600\",\n                                            children: \"总模型数\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-green-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: availableModels.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-green-600\",\n                                            children: \"可用模型\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-orange-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: models.filter((m)=>m.type === 'multimodal').length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-orange-600\",\n                                            children: \"多模态模型\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        availableModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 bg-yellow-50 border border-yellow-200 rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-800\",\n                                children: \"⚠️ 当前没有可用的模型，请检查服务器配置\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this),\n                        !selectedModel && availableModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 bg-blue-50 border border-blue-200 rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-800\",\n                                children: \"\\uD83D\\uDCA1 请选择一个模型以开始分析\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/components/ModelSelector.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_c = ModelSelector;\nvar _c;\n$RefreshReg$(_c, \"ModelSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ModelSelector.tsx\n"));

/***/ })

});