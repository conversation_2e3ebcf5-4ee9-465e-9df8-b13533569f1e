"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AudioControls__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AudioControls */ \"(app-pages-browser)/./src/components/AudioControls.tsx\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ModelSelector */ \"(app-pages-browser)/./src/components/ModelSelector.tsx\");\n/* harmony import */ var _components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TranscriptionHistory */ \"(app-pages-browser)/./src/components/TranscriptionHistory.tsx\");\n/* harmony import */ var _components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AnalysisResults */ \"(app-pages-browser)/./src/components/AnalysisResults.tsx\");\n/* harmony import */ var _components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MultimodalAnalysis */ \"(app-pages-browser)/./src/components/MultimodalAnalysis.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/store/useAppStore */ \"(app-pages-browser)/./src/store/useAppStore.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _services_websocket__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/websocket */ \"(app-pages-browser)/./src/services/websocket.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    // 视频显示引用\n    const videoDisplayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 视频帧请求定时器\n    const frameRequestInterval = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 状态管理\n    const { videoSessions, currentSessionId, audioStatus, transcriptionHistory, availableModels, selectedModel, analysisResults, isLoading, error, wsConnected, addVideoSession, removeVideoSession, setCurrentSession, updateVideoFrame, setAudioStatus, addTranscriptionResult, clearTranscriptionHistory, setAvailableModels, setSelectedModel, addAnalysisResult, clearAnalysisResults, setLoading, setError, setWsConnected, getCurrentSession, getAudioStatus, getTranscriptionHistory } = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore)();\n    // 本地状态\n    const [rtspUrl, setRtspUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rtsp://*************:554/12');\n    const [audioEnabled, setAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentSession = getCurrentSession();\n    const currentAudioStatus = currentSessionId ? getAudioStatus(currentSessionId) : null;\n    // 调试信息\n    console.log('当前状态:', {\n        currentSessionId,\n        currentSession,\n        videoSessions,\n        hasCurrentSession: !!currentSession\n    });\n    const currentTranscriptionHistory = currentSessionId ? getTranscriptionHistory(currentSessionId) : [];\n    // WebSocket 消息处理\n    const handleWebSocketMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[handleWebSocketMessage]\": (message)=>{\n            // 添加详细的消息调试\n            console.log('收到WebSocket消息:', {\n                type: message.type,\n                keys: Object.keys(message),\n                messagePreview: JSON.stringify(message).substring(0, 200) + '...'\n            });\n            if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isTranscriptionMessage)(message)) {\n                if (message.sessionId) {\n                    addTranscriptionResult(message.sessionId, message.result);\n                }\n            } else if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isMultimodalAnalysisMessage)(message)) {\n                addAnalysisResult(message.result);\n            } else if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isVideoFrameMessage)(message)) {\n                var _message_frameData;\n                // 处理视频帧数据\n                console.log('✅ 确认收到视频帧消息:', {\n                    sessionId: message.session_id,\n                    frameDataLength: ((_message_frameData = message.frameData) === null || _message_frameData === void 0 ? void 0 : _message_frameData.length) || 0,\n                    currentSessionId,\n                    messageType: message.type\n                });\n                if (currentSessionId && message.frameData) {\n                    console.log('🎬 更新视频帧数据到store');\n                    updateVideoFrame(currentSessionId, message.frameData);\n                } else {\n                    console.warn('❌ 无法更新视频帧:', {\n                        currentSessionId,\n                        hasFrameData: !!message.frameData,\n                        messageSessionId: message.session_id\n                    });\n                }\n            } else {\n                console.log('🔍 未识别的消息类型:', message.type);\n            }\n        }\n    }[\"HomePage.useCallback[handleWebSocketMessage]\"], [\n        addTranscriptionResult,\n        addAnalysisResult,\n        updateVideoFrame,\n        currentSessionId\n    ]);\n    // 请求视频帧\n    const requestVideoFrame = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[requestVideoFrame]\": ()=>{\n            if (_services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.getConnectionStatus() === 'connected' && currentSessionId) {\n                _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.send({\n                    type: 'request_frame',\n                    session_id: currentSessionId,\n                    timestamp: new Date().toISOString()\n                });\n            }\n        }\n    }[\"HomePage.useCallback[requestVideoFrame]\"], [\n        currentSessionId\n    ]);\n    // 启动帧请求定时器\n    const startFrameRequesting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[startFrameRequesting]\": (sessionId)=>{\n            if (frameRequestInterval.current) {\n                clearInterval(frameRequestInterval.current);\n            }\n            // 立即请求第一帧\n            if (_services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.getConnectionStatus() === 'connected') {\n                _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.send({\n                    type: 'request_frame',\n                    session_id: sessionId,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            // 设置定时器每100毫秒请求一次帧\n            frameRequestInterval.current = setInterval({\n                \"HomePage.useCallback[startFrameRequesting]\": ()=>{\n                    if (_services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.getConnectionStatus() === 'connected' && currentSessionId === sessionId) {\n                        requestVideoFrame();\n                    } else {\n                        stopFrameRequesting();\n                    }\n                }\n            }[\"HomePage.useCallback[startFrameRequesting]\"], 100);\n            console.log('启动视频帧请求定时器 (100ms)');\n        }\n    }[\"HomePage.useCallback[startFrameRequesting]\"], [\n        requestVideoFrame,\n        currentSessionId\n    ]);\n    // 停止帧请求定时器\n    const stopFrameRequesting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[stopFrameRequesting]\": ()=>{\n            if (frameRequestInterval.current) {\n                clearInterval(frameRequestInterval.current);\n                frameRequestInterval.current = null;\n                console.log('停止视频帧请求定时器');\n            }\n        }\n    }[\"HomePage.useCallback[stopFrameRequesting]\"], []);\n    // 视频帧显示\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            if (!currentSession) return;\n            // 如果有新的视频帧数据，直接显示\n            if (currentSession.currentFrame && videoDisplayRef.current) {\n                console.log('显示新视频帧，数据长度:', currentSession.currentFrame.length);\n                // 直接设置img标签的src为base64数据\n                videoDisplayRef.current.src = \"data:image/jpeg;base64,\".concat(currentSession.currentFrame);\n                videoDisplayRef.current.style.display = 'block';\n                console.log('视频帧已设置到img标签');\n            }\n        }\n    }[\"HomePage.useEffect\"], [\n        currentSession === null || currentSession === void 0 ? void 0 : currentSession.currentFrame\n    ]);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // 连接 WebSocket\n            _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.connect();\n            // 添加消息处理器\n            const removeMessageHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addMessageHandler(handleWebSocketMessage);\n            // 添加连接状态处理器\n            const removeConnectionHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addConnectionHandler({\n                \"HomePage.useEffect.removeConnectionHandler\": (status)=>{\n                    setWsConnected(status === 'connected');\n                }\n            }[\"HomePage.useEffect.removeConnectionHandler\"]);\n            // 加载可用模型\n            loadModels();\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    removeMessageHandler();\n                    removeConnectionHandler();\n                    stopFrameRequesting(); // 清理帧请求定时器\n                    _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.disconnect();\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        handleWebSocketMessage,\n        setWsConnected,\n        stopFrameRequesting\n    ]);\n    // 加载模型列表\n    const loadModels = async ()=>{\n        try {\n            const models = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.getAvailableModels();\n            // 确保 models 是数组\n            const modelArray = Array.isArray(models) ? models : [];\n            setAvailableModels(modelArray);\n            // 自动选择第一个可用的多模态模型\n            const multimodalModel = modelArray.find((m)=>m.type === 'multimodal' && m.isAvailable);\n            if (multimodalModel && !selectedModel) {\n                setSelectedModel(multimodalModel.id);\n            }\n        } catch (error) {\n            console.error('Failed to load models:', error);\n            setError('加载模型列表失败');\n            // 设置默认的模型数据以便测试\n            setAvailableModels([\n                {\n                    id: 'test-multimodal',\n                    name: '测试多模态模型',\n                    type: 'multimodal',\n                    provider: 'Test',\n                    description: '用于测试的模拟模型',\n                    isAvailable: true\n                },\n                {\n                    id: 'test-vision',\n                    name: '测试视觉模型',\n                    type: 'vision',\n                    provider: 'Test',\n                    description: '用于测试的视觉模型',\n                    isAvailable: true\n                }\n            ]);\n        }\n    };\n    // 打开视频流\n    const handleOpenVideo = async ()=>{\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.isValidRtspUrl)(rtspUrl)) {\n            setError('请输入有效的RTSP地址');\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        console.log('开始打开视频流:', rtspUrl);\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.openVideo({\n                rtsp_url: rtspUrl,\n                enable_audio: audioEnabled\n            });\n            console.log('API响应:', response);\n            if (response.success) {\n                const session = {\n                    id: response.session_id,\n                    rtspUrl,\n                    isActive: true,\n                    audioEnabled,\n                    createdAt: new Date()\n                };\n                console.log('创建会话:', session);\n                addVideoSession(session);\n                setCurrentSession(response.session_id);\n                // 设置音频状态\n                if (response.audio_status) {\n                    setAudioStatus(response.session_id, response.audio_status);\n                }\n                // 启动视频帧请求\n                startFrameRequesting(response.session_id);\n                console.log('视频流打开成功');\n            } else {\n                console.error('API返回失败:', response);\n                setError(response.message || '打开视频流失败');\n            }\n        } catch (error) {\n            console.error('API调用失败:', error);\n            setError('连接视频流失败，请检查网络和RTSP地址');\n            // 为了测试，我们可以创建一个模拟会话\n            console.log('创建模拟会话进行测试');\n            const mockSession = {\n                id: \"mock-\".concat(Date.now()),\n                rtspUrl,\n                isActive: true,\n                audioEnabled,\n                createdAt: new Date()\n            };\n            addVideoSession(mockSession);\n            setCurrentSession(mockSession.id);\n            // 即使是模拟会话也启动帧请求（用于测试）\n            startFrameRequesting(mockSession.id);\n            setError('使用模拟视频流（后端连接失败）');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 关闭视频流\n    const handleCloseVideo = async ()=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            // 停止帧请求\n            stopFrameRequesting();\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.closeVideo(currentSessionId);\n            removeVideoSession(currentSessionId);\n            setCurrentSession(null);\n        } catch (error) {\n            console.error('Failed to close video:', error);\n            setError('关闭视频流失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 开始音频转录\n    const handleStartTranscription = async (language)=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.startAudioTranscription({\n                session_id: currentSessionId,\n                language\n            });\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: true,\n                    language\n                });\n            }\n        } catch (error) {\n            setError('启动音频转录失败');\n        }\n    };\n    // 停止音频转录\n    const handleStopTranscription = async ()=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.stopAudioTranscription(currentSessionId);\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: false\n                });\n            }\n        } catch (error) {\n            setError('停止音频转录失败');\n        }\n    };\n    // 多模态分析\n    const handleMultimodalAnalysis = async (prompt, includeAudio, contextSeconds)=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.analyzeMultimodal({\n                session_id: currentSessionId,\n                prompt,\n                include_recent_audio: includeAudio,\n                audio_context_seconds: contextSeconds\n            });\n            addAnalysisResult(result);\n        } catch (error) {\n            setError('多模态分析失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"VA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"视频分析系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                        variant: wsConnected ? \"success\" : \"danger\",\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            wsConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 32\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 63\n                                            }, this),\n                                            wsConnected ? '已连接' : '未连接'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        size: \"sm\",\n                                        variant: \"ghost\",\n                                        icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-5 h-5 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>setError(null),\n                                className: \"ml-auto\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    children: \"视频流控制\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        !currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium text-blue-900\",\n                                                                            children: \"第一步：打开视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                                    children: '输入RTSP地址，选择是否启用音频，然后点击\"打开视频流\"按钮'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    label: \"RTSP地址\",\n                                                                    value: rtspUrl,\n                                                                    onChange: (e)=>setRtspUrl(e.target.value),\n                                                                    placeholder: \"rtsp://*************:554/12\",\n                                                                    disabled: !!currentSession\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-end gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center gap-2 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: audioEnabled,\n                                                                                onChange: (e)=>setAudioEnabled(e.target.checked),\n                                                                                disabled: !!currentSession,\n                                                                                className: \"rounded\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 504,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"启用音频处理\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: !currentSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleOpenVideo,\n                                                                loading: isLoading,\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"打开视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleCloseVideo,\n                                                                loading: isLoading,\n                                                                variant: \"danger\",\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"关闭视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                            children: \"实时视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"RTSP地址: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-mono text-xs\",\n                                                                                    children: currentSession.rtspUrl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 545,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 544,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative bg-black rounded-lg overflow-hidden aspect-video border-2 border-gray-300 shadow-lg\",\n                                                                    children: [\n                                                                        (currentSession === null || currentSession === void 0 ? void 0 : currentSession.currentFrame) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    ref: videoDisplayRef,\n                                                                                    alt: \"RTSP视频流\",\n                                                                                    className: \"w-full h-full object-contain\",\n                                                                                    onLoad: ()=>console.log('视频帧显示成功'),\n                                                                                    onError: (e)=>{\n                                                                                        var _currentSession_currentFrame;\n                                                                                        console.error('视频帧显示失败:', e);\n                                                                                        console.log('当前帧数据长度:', (currentSession === null || currentSession === void 0 ? void 0 : (_currentSession_currentFrame = currentSession.currentFrame) === null || _currentSession_currentFrame === void 0 ? void 0 : _currentSession_currentFrame.length) || 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 553,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute top-2 right-2 bg-black bg-opacity-70 text-white px-3 py-2 rounded-lg text-sm\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                                    lineNumber: 567,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-semibold\",\n                                                                                                    children: \"LIVE\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                                    lineNumber: 568,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 566,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-gray-300 mt-1\",\n                                                                                            children: [\n                                                                                                \"会话: \",\n                                                                                                currentSession.id.slice(0, 8),\n                                                                                                \"...\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 570,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 565,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full h-full flex items-center justify-center text-white\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 578,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-semibold\",\n                                                                                        children: \"等待视频流数据...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 579,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-400 mt-2\",\n                                                                                        children: \"请确保RTSP地址正确且网络连接正常\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 580,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 577,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"success\",\n                                                                                className: \"bg-green-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 591,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"实时播放\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 587,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        currentSession.audioEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 right-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"primary\",\n                                                                                className: \"bg-blue-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 603,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"音频已启用\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 599,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 right-4 flex gap-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                                size: \"sm\",\n                                                                                variant: \"secondary\",\n                                                                                className: \"bg-black bg-opacity-50 text-white border-gray-600 hover:bg-opacity-70\",\n                                                                                onClick: ()=>{\n                                                                                    const img = videoDisplayRef.current;\n                                                                                    if (img && (currentSession === null || currentSession === void 0 ? void 0 : currentSession.currentFrame)) {\n                                                                                        // 创建Canvas来生成截图\n                                                                                        const canvas = document.createElement('canvas');\n                                                                                        const ctx = canvas.getContext('2d');\n                                                                                        if (ctx) {\n                                                                                            canvas.width = img.naturalWidth;\n                                                                                            canvas.height = img.naturalHeight;\n                                                                                            ctx.drawImage(img, 0, 0);\n                                                                                            const link = document.createElement('a');\n                                                                                            link.download = \"rtsp-screenshot-\".concat(Date.now(), \".png\");\n                                                                                            link.href = canvas.toDataURL();\n                                                                                            link.click();\n                                                                                        }\n                                                                                    }\n                                                                                },\n                                                                                children: \"截图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 611,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-black bg-opacity-70 text-white px-3 py-2 rounded text-sm shadow-md\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"flex items-center gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                                    lineNumber: 643,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                \"会话: \",\n                                                                                                currentSession.id.slice(0, 8),\n                                                                                                \"...\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 642,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"1280x720\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 646,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: new Date().toLocaleTimeString()\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 647,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 641,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 640,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this),\n                                    currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-blue-900\",\n                                                                children: \"视频流已打开，现在可以进行智能分析\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                        children: '请输入分析提示词或选择预设提示词，然后点击\"开始分析\"按钮'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__.MultimodalAnalysis, {\n                                                sessionId: currentSessionId,\n                                                audioEnabled: currentSession.audioEnabled || false,\n                                                onAnalyze: handleMultimodalAnalysis,\n                                                disabled: false,\n                                                loading: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 15\n                                    }, this),\n                                    analysisResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-green-50 border border-green-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-green-900\",\n                                                                children: \"分析结果\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700 mt-2 ml-8\",\n                                                        children: \"大模型实时输出的分析结果如下\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__.AnalysisResults, {\n                                                results: analysisResults,\n                                                onClear: clearAnalysisResults\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__.ModelSelector, {\n                                        models: availableModels,\n                                        selectedModel: selectedModel,\n                                        onModelSelect: setSelectedModel\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 13\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && currentAudioStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioControls__WEBPACK_IMPORTED_MODULE_2__.AudioControls, {\n                                        sessionId: currentSession.id,\n                                        audioStatus: currentAudioStatus,\n                                        onStartTranscription: handleStartTranscription,\n                                        onStopTranscription: handleStopTranscription,\n                                        onLanguageChange: ()=>{}\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 15\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__.TranscriptionHistory, {\n                                        history: currentTranscriptionHistory,\n                                        onClear: ()=>currentSessionId && clearTranscriptionHistory(currentSessionId)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n        lineNumber: 416,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"sWQEa9mAZY9X0NDtgNO5AYBHUIg=\", false, function() {\n    return [\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});