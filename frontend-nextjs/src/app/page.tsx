'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { VideoPlayer } from '@/components/VideoPlayer';
import { AudioControls } from '@/components/AudioControls';
import { ModelSelector } from '@/components/ModelSelector';
import { TranscriptionHistory } from '@/components/TranscriptionHistory';
import { AnalysisResults } from '@/components/AnalysisResults';
import { MultimodalAnalysis } from '@/components/MultimodalAnalysis';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import {
  Play,
  Square,
  Settings,
  Wifi,
  WifiOff,
  AlertCircle,
  CheckCircle,
  Volume2
} from 'lucide-react';

import { useAppStore } from '@/store/useAppStore';
import { apiService } from '@/services/api';
import { wsService, isTranscriptionMessage, isMultimodalAnalysisMessage, isVideoFrameMessage } from '@/services/websocket';
import { generateId, isValidRtspUrl } from '@/lib/utils';
import type { Language, VideoSession, AudioStatus, TranscriptionResult, MultimodalAnalysisResult } from '@/types';

export default function HomePage() {
  // 视频显示引用
  const videoDisplayRef = useRef<HTMLImageElement>(null);

  // 视频帧请求定时器
  const frameRequestInterval = useRef<NodeJS.Timeout | null>(null);

  // 状态管理
  const {
    videoSessions,
    currentSessionId,
    audioStatus,
    transcriptionHistory,
    availableModels,
    selectedModel,
    analysisResults,
    isLoading,
    error,
    wsConnected,
    addVideoSession,
    removeVideoSession,
    setCurrentSession,
    updateVideoFrame,
    setAudioStatus,
    addTranscriptionResult,
    clearTranscriptionHistory,
    setAvailableModels,
    setSelectedModel,
    addAnalysisResult,
    clearAnalysisResults,
    setLoading,
    setError,
    setWsConnected,
    getCurrentSession,
    getAudioStatus,
    getTranscriptionHistory,
  } = useAppStore();

  // 本地状态
  const [rtspUrl, setRtspUrl] = useState('rtsp://*************:554/12');
  const [audioEnabled, setAudioEnabled] = useState(false);

  const currentSession = getCurrentSession();
  const currentAudioStatus = currentSessionId ? getAudioStatus(currentSessionId) : null;

  // 调试信息
  console.log('当前状态:', {
    currentSessionId,
    currentSession,
    videoSessions,
    hasCurrentSession: !!currentSession
  });
  const currentTranscriptionHistory = currentSessionId ? getTranscriptionHistory(currentSessionId) : [];

  // WebSocket 消息处理
  const handleWebSocketMessage = useCallback((message: any) => {
    try {
      // 对于视频帧消息，只记录基本信息避免日志过大
      if (message.type === 'video_frame') {
        console.log('📺 收到视频帧消息:', {
          type: message.type,
          sessionId: message.session_id,
          frameDataLength: message.frameData?.length || 0,
          timestamp: message.timestamp
        });
      } else {
        console.log('📨 收到WebSocket消息:', {
          type: message.type,
          keys: Object.keys(message),
          messagePreview: JSON.stringify(message).substring(0, 200) + '...'
        });
      }

      if (message.type === 'connection_established') {
        console.log('🎉 WebSocket连接已建立:', message);
      } else if (isTranscriptionMessage(message)) {
        if (message.sessionId) {
          addTranscriptionResult(message.sessionId, message.result);
        }
      } else if (isMultimodalAnalysisMessage(message)) {
        addAnalysisResult(message.result);
      } else if (isVideoFrameMessage(message)) {
        // 处理视频帧数据
        console.log('✅ 确认收到视频帧消息:', {
          sessionId: message.session_id,
          frameDataLength: message.frameData?.length || 0,
          currentSessionId,
          messageType: message.type
        });

        if (currentSessionId && message.frameData) {
          console.log('🎬 更新视频帧数据到store');
          updateVideoFrame(currentSessionId, message.frameData);
        } else {
          console.warn('❌ 无法更新视频帧:', {
            currentSessionId,
            hasFrameData: !!message.frameData,
            messageSessionId: message.session_id
          });
        }
      } else if (message.type === 'error') {
        console.error('❌ 后端错误消息:', message);
      } else {
        console.log('🔍 未识别的消息类型:', message.type, message);
      }
    } catch (error) {
      console.error('❌ WebSocket消息处理错误:', error);
      console.error('错误的消息内容:', message);
    }
  }, [addTranscriptionResult, addAnalysisResult, updateVideoFrame, currentSessionId]);

  // 请求视频帧
  const requestVideoFrame = useCallback(() => {
    const connectionStatus = wsService.getConnectionStatus();
    console.log('🎬 请求视频帧:', {
      connectionStatus,
      currentSessionId,
      canSend: connectionStatus === 'connected' && !!currentSessionId
    });

    if (connectionStatus === 'connected' && currentSessionId) {
      const message = {
        type: 'request_frame',
        session_id: currentSessionId,
        timestamp: new Date().toISOString()
      };
      console.log('📤 发送帧请求消息:', message);
      const sent = wsService.send(message);
      console.log('📤 消息发送结果:', sent);
    } else {
      console.warn('❌ 无法发送帧请求:', { connectionStatus, currentSessionId });
    }
  }, [currentSessionId]);

  // 启动帧请求定时器
  const startFrameRequesting = useCallback((sessionId: string) => {
    console.log('🚀 启动帧请求系统:', {
      sessionId,
      currentSessionId,
      wsStatus: wsService.getConnectionStatus()
    });

    if (frameRequestInterval.current) {
      clearInterval(frameRequestInterval.current);
    }

    // 立即请求第一帧
    const connectionStatus = wsService.getConnectionStatus();
    if (connectionStatus === 'connected') {
      const message = {
        type: 'request_frame',
        session_id: sessionId,
        timestamp: new Date().toISOString()
      };
      console.log('📤 立即发送第一个帧请求:', message);
      wsService.send(message);
    } else {
      console.warn('⚠️ WebSocket未连接，无法立即请求帧:', connectionStatus);
    }

    // 设置定时器每100毫秒请求一次帧
    frameRequestInterval.current = setInterval(() => {
      if (wsService.getConnectionStatus() === 'connected' && currentSessionId === sessionId) {
        requestVideoFrame();
      } else {
        console.log('⏹️ 停止帧请求 - 连接状态或会话变化');
        stopFrameRequesting();
      }
    }, 100);

    console.log('✅ 视频帧请求定时器已启动 (100ms间隔)');
  }, [requestVideoFrame, currentSessionId]);

  // 停止帧请求定时器
  const stopFrameRequesting = useCallback(() => {
    if (frameRequestInterval.current) {
      clearInterval(frameRequestInterval.current);
      frameRequestInterval.current = null;
      console.log('停止视频帧请求定时器');
    }
  }, []);

  // 视频帧显示
  useEffect(() => {
    console.log('🎥 视频帧显示useEffect触发:', {
      hasCurrentSession: !!currentSession,
      hasCurrentFrame: !!currentSession?.currentFrame,
      frameLength: currentSession?.currentFrame?.length || 0,
      hasVideoDisplayRef: !!videoDisplayRef.current
    });

    if (!currentSession) {
      console.log('❌ 没有当前会话');
      return;
    }

    // 如果有新的视频帧数据，直接显示
    if (currentSession.currentFrame && videoDisplayRef.current) {
      console.log('🖼️ 显示新视频帧，数据长度:', currentSession.currentFrame.length);

      // 直接设置img标签的src为base64数据
      videoDisplayRef.current.src = `data:image/jpeg;base64,${currentSession.currentFrame}`;
      videoDisplayRef.current.style.display = 'block';

      console.log('✅ 视频帧已设置到img标签');
    } else {
      console.log('⏳ 等待视频帧数据...', {
        hasFrame: !!currentSession.currentFrame,
        hasRef: !!videoDisplayRef.current
      });
    }
  }, [currentSession?.currentFrame]);



  // 初始化
  useEffect(() => {
    // 连接 WebSocket
    wsService.connect();
    
    // 添加消息处理器
    const removeMessageHandler = wsService.addMessageHandler(handleWebSocketMessage);
    
    // 添加连接状态处理器
    const removeConnectionHandler = wsService.addConnectionHandler((status) => {
      console.log('🔌 WebSocket连接状态变化:', status);
      setWsConnected(status === 'connected');

      if (status === 'connected') {
        console.log('✅ WebSocket连接成功');
      } else if (status === 'disconnected') {
        console.log('❌ WebSocket连接断开');
      } else if (status === 'error') {
        console.log('⚠️ WebSocket连接错误');
      }
    });

    // 加载可用模型
    loadModels();

    return () => {
      removeMessageHandler();
      removeConnectionHandler();
      stopFrameRequesting(); // 清理帧请求定时器
      wsService.disconnect();
    };
  }, [handleWebSocketMessage, setWsConnected, stopFrameRequesting]);

  // 加载模型列表
  const loadModels = async () => {
    try {
      const models = await apiService.getAvailableModels();

      // 确保 models 是数组
      const modelArray = Array.isArray(models) ? models : [];
      setAvailableModels(modelArray);

      // 自动选择第一个可用的多模态模型
      const multimodalModel = modelArray.find(m => m.type === 'multimodal' && m.isAvailable);
      if (multimodalModel && !selectedModel) {
        setSelectedModel(multimodalModel.id);
      }
    } catch (error) {
      console.error('Failed to load models:', error);
      setError('加载模型列表失败');

      // 设置默认的模型数据以便测试
      setAvailableModels([
        {
          id: 'test-multimodal',
          name: '测试多模态模型',
          type: 'multimodal',
          provider: 'Test',
          description: '用于测试的模拟模型',
          isAvailable: true
        },
        {
          id: 'test-vision',
          name: '测试视觉模型',
          type: 'vision',
          provider: 'Test',
          description: '用于测试的视觉模型',
          isAvailable: true
        }
      ]);
    }
  };

  // 打开视频流
  const handleOpenVideo = async () => {
    if (!isValidRtspUrl(rtspUrl)) {
      setError('请输入有效的RTSP地址');
      return;
    }

    setLoading(true);
    setError(null);
    console.log('开始打开视频流:', rtspUrl);

    try {
      console.log('📡 发送API请求:', {
        rtsp_url: rtspUrl,
        enable_audio: audioEnabled,
        endpoint: '/video/open'
      });

      const response = await apiService.openVideo({
        rtsp_url: rtspUrl,
        enable_audio: audioEnabled,
      });

      console.log('📥 API响应:', response);
      console.log('📋 API响应详细信息:', {
        success: response.success,
        session_id: response.session_id,
        message: response.message,
        keys: Object.keys(response),
        fullResponse: JSON.stringify(response, null, 2)
      });

      if (response.success) {
        const session: VideoSession = {
          id: response.session_id,
          rtspUrl,
          isActive: true,
          audioEnabled,
          createdAt: new Date(),
        };

        console.log('创建会话:', session);
        addVideoSession(session);
        setCurrentSession(response.session_id);

        // 设置音频状态
        if (response.audio_status) {
          setAudioStatus(response.session_id, response.audio_status);
        }

        // 启动视频帧请求
        console.log('🚀 准备启动帧请求:', {
          sessionId: response.session_id,
          wsConnected: wsConnected,
          wsStatus: wsService.getConnectionStatus()
        });
        startFrameRequesting(response.session_id);

        console.log('✅ 视频流打开成功，帧请求已启动');
      } else {
        console.error('❌ API返回失败:', response);
        console.error('❌ 失败详情:', {
          success: response.success,
          message: response.message,
          session_id: response.session_id
        });
        setError(response.message || '打开视频流失败');
      }
    } catch (error) {
      console.error('❌ API调用失败:', error);
      console.error('❌ 错误详情:', {
        name: error?.name,
        message: error?.message,
        stack: error?.stack
      });
      setError('连接视频流失败，请检查网络和RTSP地址');

      // 为了测试，我们可以创建一个模拟会话
      console.log('创建模拟会话进行测试');
      const mockSession: VideoSession = {
        id: `mock-${Date.now()}`,
        rtspUrl,
        isActive: true,
        audioEnabled,
        createdAt: new Date(),
      };

      addVideoSession(mockSession);
      setCurrentSession(mockSession.id);

      // 即使是模拟会话也启动帧请求（用于测试）
      startFrameRequesting(mockSession.id);

      setError('使用模拟视频流（后端连接失败）');
    } finally {
      setLoading(false);
    }
  };

  // 关闭视频流
  const handleCloseVideo = async () => {
    if (!currentSessionId) return;

    setLoading(true);

    try {
      // 停止帧请求
      stopFrameRequesting();

      await apiService.closeVideo(currentSessionId);
      removeVideoSession(currentSessionId);
      setCurrentSession(null);
    } catch (error) {
      console.error('Failed to close video:', error);
      setError('关闭视频流失败');
    } finally {
      setLoading(false);
    }
  };

  // 开始音频转录
  const handleStartTranscription = async (language: Language) => {
    if (!currentSessionId) return;

    try {
      await apiService.startAudioTranscription({
        session_id: currentSessionId,
        language,
      });

      // 更新音频状态
      const currentStatus = getAudioStatus(currentSessionId);
      if (currentStatus) {
        setAudioStatus(currentSessionId, {
          ...currentStatus,
          transcriptionActive: true,
          language,
        });
      }
    } catch (error) {
      setError('启动音频转录失败');
    }
  };

  // 停止音频转录
  const handleStopTranscription = async () => {
    if (!currentSessionId) return;

    try {
      await apiService.stopAudioTranscription(currentSessionId);

      // 更新音频状态
      const currentStatus = getAudioStatus(currentSessionId);
      if (currentStatus) {
        setAudioStatus(currentSessionId, {
          ...currentStatus,
          transcriptionActive: false,
        });
      }
    } catch (error) {
      setError('停止音频转录失败');
    }
  };

  // 多模态分析
  const handleMultimodalAnalysis = async (
    prompt: string,
    includeAudio: boolean,
    contextSeconds: number
  ) => {
    if (!currentSessionId) return;

    setLoading(true);

    try {
      const result = await apiService.analyzeMultimodal({
        session_id: currentSessionId,
        prompt,
        include_recent_audio: includeAudio,
        audio_context_seconds: contextSeconds,
      });

      addAnalysisResult(result);
    } catch (error) {
      setError('多模态分析失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">VA</span>
              </div>
              <h1 className="text-xl font-semibold text-gray-900">
                视频分析系统
              </h1>
            </div>
            
            <div className="flex items-center gap-4">
              {/* WebSocket 状态 */}
              <Badge 
                variant={wsConnected ? "success" : "danger"}
                className="flex items-center gap-1"
              >
                {wsConnected ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                {wsConnected ? '已连接' : '未连接'}
              </Badge>
              
              <Button size="sm" variant="ghost" icon={Settings}>
                设置
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setError(null)}
              className="ml-auto"
            >
              ✕
            </Button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧列 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 视频控制 */}
            <Card>
              <CardHeader>
                <CardTitle>视频流控制</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* 步骤指示 */}
                  {!currentSession && (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          1
                        </div>
                        <h3 className="font-medium text-blue-900">
                          第一步：打开视频流
                        </h3>
                      </div>
                      <p className="text-sm text-blue-700 mt-2 ml-8">
                        输入RTSP地址，选择是否启用音频，然后点击"打开视频流"按钮
                      </p>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="RTSP地址"
                      value={rtspUrl}
                      onChange={(e) => setRtspUrl(e.target.value)}
                      placeholder="rtsp://*************:554/12"
                      disabled={!!currentSession}
                    />

                    <div className="flex items-end gap-2">
                      <label className="flex items-center gap-2 text-sm">
                        <input
                          type="checkbox"
                          checked={audioEnabled}
                          onChange={(e) => setAudioEnabled(e.target.checked)}
                          disabled={!!currentSession}
                          className="rounded"
                        />
                        启用音频处理
                      </label>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {!currentSession ? (
                      <Button
                        onClick={handleOpenVideo}
                        loading={isLoading}
                        icon={Play}
                        className="flex-1"
                      >
                        打开视频流
                      </Button>
                    ) : (
                      <Button
                        onClick={handleCloseVideo}
                        loading={isLoading}
                        variant="danger"
                        icon={Square}
                        className="flex-1"
                      >
                        关闭视频流
                      </Button>
                    )}
                  </div>

                  {/* 视频播放区域 - 直接在按钮下方显示 */}
                  {currentSession && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="mb-4">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">实时视频流</h3>
                        <p className="text-sm text-gray-600">
                          RTSP地址: <span className="font-mono text-xs">{currentSession.rtspUrl}</span>
                        </p>
                      </div>

                      {/* 视频显示区域 */}
                      <div className="relative bg-black rounded-lg overflow-hidden aspect-video border-2 border-gray-300 shadow-lg">
                        {currentSession?.currentFrame ? (
                          <>
                            <img
                              ref={videoDisplayRef}
                              alt="RTSP视频流"
                              className="w-full h-full object-contain"
                              onLoad={() => console.log('视频帧显示成功')}
                              onError={(e) => {
                                console.error('视频帧显示失败:', e);
                                console.log('当前帧数据长度:', currentSession?.currentFrame?.length || 0);
                              }}
                            />

                            {/* 状态信息覆盖层 */}
                            <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white px-3 py-2 rounded-lg text-sm">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                <span className="font-semibold">LIVE</span>
                              </div>
                              <div className="text-xs text-gray-300 mt-1">
                                会话: {currentSession.id.slice(0, 8)}...
                              </div>
                            </div>
                          </>
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-white">
                            <div className="text-center">
                              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
                              <p className="text-lg font-semibold">等待视频流数据...</p>
                              <p className="text-sm text-gray-400 mt-2">请确保RTSP地址正确且网络连接正常</p>
                            </div>
                          </div>
                        )}

                        {/* 视频状态覆盖层 */}
                        <div className="absolute top-4 left-4">
                          <Badge
                            variant="success"
                            className="bg-green-600 text-white flex items-center gap-1 shadow-md"
                          >
                            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                            实时播放
                          </Badge>
                        </div>

                        {/* 音频状态指示 */}
                        {currentSession.audioEnabled && (
                          <div className="absolute top-4 right-4">
                            <Badge
                              variant="primary"
                              className="bg-blue-600 text-white flex items-center gap-1 shadow-md"
                            >
                              <Volume2 className="w-3 h-3" />
                              音频已启用
                            </Badge>
                          </div>
                        )}

                        {/* 视频控制按钮 */}
                        <div className="absolute bottom-4 right-4 flex gap-2">
                          <Button
                            size="sm"
                            variant="secondary"
                            className="bg-black bg-opacity-50 text-white border-gray-600 hover:bg-opacity-70"
                            onClick={() => {
                              const img = videoDisplayRef.current;
                              if (img && currentSession?.currentFrame) {
                                // 创建Canvas来生成截图
                                const canvas = document.createElement('canvas');
                                const ctx = canvas.getContext('2d');
                                if (ctx) {
                                  canvas.width = img.naturalWidth;
                                  canvas.height = img.naturalHeight;
                                  ctx.drawImage(img, 0, 0);

                                  const link = document.createElement('a');
                                  link.download = `rtsp-screenshot-${Date.now()}.png`;
                                  link.href = canvas.toDataURL();
                                  link.click();
                                }
                              }
                            }}
                          >
                            截图
                          </Button>
                        </div>

                        {/* 视频信息覆盖层 */}
                        <div className="absolute bottom-4 left-4">
                          <div className="bg-black bg-opacity-70 text-white px-3 py-2 rounded text-sm shadow-md">
                            <div className="flex items-center gap-4">
                              <span className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-green-400 rounded-full" />
                                会话: {currentSession.id.slice(0, 8)}...
                              </span>
                              <span>1280x720</span>
                              <span>{new Date().toLocaleTimeString()}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 多模态分析 - 只在有视频会话时显示 */}
            {currentSession && (
              <div className="space-y-4">
                {/* 步骤指示 */}
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      2
                    </div>
                    <h3 className="font-medium text-blue-900">
                      视频流已打开，现在可以进行智能分析
                    </h3>
                  </div>
                  <p className="text-sm text-blue-700 mt-2 ml-8">
                    请输入分析提示词或选择预设提示词，然后点击"开始分析"按钮
                  </p>
                </div>

                <MultimodalAnalysis
                  sessionId={currentSessionId}
                  audioEnabled={currentSession.audioEnabled || false}
                  onAnalyze={handleMultimodalAnalysis}
                  disabled={false}
                  loading={isLoading}
                />
              </div>
            )}

            {/* 分析结果 - 只在有分析结果时显示 */}
            {analysisResults.length > 0 && (
              <div className="space-y-4">
                {/* 步骤指示 */}
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      3
                    </div>
                    <h3 className="font-medium text-green-900">
                      分析结果
                    </h3>
                  </div>
                  <p className="text-sm text-green-700 mt-2 ml-8">
                    大模型实时输出的分析结果如下
                  </p>
                </div>

                <AnalysisResults
                  results={analysisResults}
                  onClear={clearAnalysisResults}
                />
              </div>
            )}
          </div>

          {/* 右侧列 */}
          <div className="space-y-6">
            {/* 模型选择 */}
            <ModelSelector
              models={availableModels}
              selectedModel={selectedModel}
              onModelSelect={setSelectedModel}
            />

            {/* 音频控制 */}
            {currentSession?.audioEnabled && currentAudioStatus && (
              <AudioControls
                sessionId={currentSession.id}
                audioStatus={currentAudioStatus}
                onStartTranscription={handleStartTranscription}
                onStopTranscription={handleStopTranscription}
                onLanguageChange={() => {}} // 语言变更在开始转录时处理
              />
            )}

            {/* 转录历史 */}
            {currentSession?.audioEnabled && (
              <TranscriptionHistory
                history={currentTranscriptionHistory}
                onClear={() => currentSessionId && clearTranscriptionHistory(currentSessionId)}
              />
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
