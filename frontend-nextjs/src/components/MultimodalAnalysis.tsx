'use client';

import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { 
  Brain, 
  Send, 
  Volume2, 
  VolumeX,
  Settings,
  Sparkles
} from 'lucide-react';

interface MultimodalAnalysisProps {
  sessionId: string | null;
  audioEnabled: boolean;
  onAnalyze: (prompt: string, includeAudio: boolean, contextSeconds: number) => void;
  disabled?: boolean;
  loading?: boolean;
}

export function MultimodalAnalysis({
  sessionId,
  audioEnabled,
  onAnalyze,
  disabled = false,
  loading = false,
}: MultimodalAnalysisProps) {
  const [prompt, setPrompt] = useState('');
  const [includeAudio, setIncludeAudio] = useState(true);
  const [contextSeconds, setContextSeconds] = useState(30);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || !sessionId) return;
    
    onAnalyze(prompt.trim(), includeAudio && audioEnabled, contextSeconds);
  };

  const presetPrompts = [
    '描述当前画面中的主要内容',
    '分析画面中的人物行为和表情',
    '识别画面中的物体和场景',
    '总结视频和音频的关键信息',
    '检测是否有异常情况',
  ];

  const handlePresetPrompt = (presetPrompt: string) => {
    setPrompt(presetPrompt);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            多模态分析
            {audioEnabled && includeAudio && (
              <Badge variant="success" className="flex items-center gap-1">
                <Volume2 className="w-3 h-3" />
                音频+视频
              </Badge>
            )}
          </CardTitle>
          
          <Button
            size="sm"
            variant="ghost"
            icon={Settings}
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            {showAdvanced ? '简化' : '高级'}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 提示词输入 */}
          <div>
            <Input
              label="分析提示词"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="请输入您想要分析的内容..."
              disabled={disabled || loading}
            />
          </div>
          
          {/* 预设提示词 */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              快速选择
            </label>
            <div className="flex flex-wrap gap-2">
              {presetPrompts.map((presetPrompt, index) => (
                <Button
                  key={index}
                  type="button"
                  size="sm"
                  variant="ghost"
                  onClick={() => handlePresetPrompt(presetPrompt)}
                  disabled={disabled || loading}
                  className="text-xs"
                >
                  {presetPrompt}
                </Button>
              ))}
            </div>
          </div>
          
          {/* 高级设置 */}
          {showAdvanced && (
            <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900">高级设置</h4>
              
              {/* 音频包含选项 */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    包含音频分析
                  </label>
                  <p className="text-xs text-gray-500">
                    将最近的音频转录结果包含在分析中
                  </p>
                </div>
                <Button
                  type="button"
                  size="sm"
                  variant={includeAudio ? "success" : "secondary"}
                  icon={includeAudio ? Volume2 : VolumeX}
                  onClick={() => setIncludeAudio(!includeAudio)}
                  disabled={!audioEnabled || disabled || loading}
                >
                  {includeAudio ? '已启用' : '已禁用'}
                </Button>
              </div>
              
              {/* 音频上下文时长 */}
              {includeAudio && audioEnabled && (
                <div>
                  <Input
                    type="number"
                    label="音频上下文时长 (秒)"
                    value={contextSeconds}
                    onChange={(e) => setContextSeconds(Number(e.target.value))}
                    min={5}
                    max={300}
                    disabled={disabled || loading}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    包含最近 {contextSeconds} 秒内的音频转录结果
                  </p>
                </div>
              )}
            </div>
          )}
          
          {/* 提交按钮 */}
          <div className="flex gap-2">
            <Button
              type="submit"
              variant="primary"
              icon={loading ? undefined : Sparkles}
              loading={loading}
              disabled={!prompt.trim() || !sessionId || disabled}
              className="flex-1"
            >
              {loading ? '分析中...' : '开始分析'}
            </Button>
          </div>
          
          {/* 状态提示 */}
          <div className="space-y-2">
            {!sessionId && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  ⚠️ 请先打开视频流
                </p>
              </div>
            )}
            
            {sessionId && !audioEnabled && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  💡 当前仅进行视频分析，如需音频分析请在打开视频时启用音频
                </p>
              </div>
            )}
            
            {sessionId && audioEnabled && includeAudio && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-800">
                  ✅ 将进行视频+音频的多模态分析
                </p>
              </div>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
