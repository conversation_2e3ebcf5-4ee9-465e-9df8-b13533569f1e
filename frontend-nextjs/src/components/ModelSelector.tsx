'use client';

import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Select } from '@/components/ui/Select';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Brain, RefreshCw, CheckCircle, XCircle } from 'lucide-react';
import type { ModelSelectorProps, ModelInfo } from '@/types';

export function ModelSelector({
  models,
  selectedModel,
  onModelSelect,
  disabled = false,
}: ModelSelectorProps) {
  // 确保 models 是数组
  const modelArray = Array.isArray(models) ? models : [];
  const selectedModelInfo = modelArray.find(model => model.id === selectedModel);

  const getModelTypeIcon = (type: ModelInfo['type']) => {
    switch (type) {
      case 'vision':
        return '👁️';
      case 'language':
        return '💬';
      case 'multimodal':
        return '🧠';
      default:
        return '🤖';
    }
  };

  const getModelTypeBadge = (type: ModelInfo['type']) => {
    const variants = {
      vision: 'primary' as const,
      language: 'success' as const,
      multimodal: 'warning' as const,
    };
    
    const labels = {
      vision: '视觉',
      language: '语言',
      multimodal: '多模态',
    };

    return (
      <Badge variant={variants[type] || 'secondary'}>
        {labels[type] || type}
      </Badge>
    );
  };

  const availableModels = modelArray.filter(model => model.isAvailable);
  const unavailableModels = modelArray.filter(model => !model.isAvailable);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            模型选择
          </CardTitle>
          
          <Button
            size="sm"
            variant="ghost"
            icon={RefreshCw}
            disabled={disabled}
          >
            刷新
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* 模型选择下拉框 */}
          <div>
            <Select
              label="选择AI模型"
              value={selectedModel || ''}
              onChange={(e) => onModelSelect(e.target.value)}
              options={[
                ...availableModels.map(model => ({
                  value: model.id,
                  label: `${getModelTypeIcon(model.type)} ${model.name} (${model.provider})`,
                })),
                ...(unavailableModels.length > 0 ? [
                  { value: '', label: '--- 不可用的模型 ---', disabled: true },
                  ...unavailableModels.map(model => ({
                    value: model.id,
                    label: `${getModelTypeIcon(model.type)} ${model.name} (${model.provider}) - 不可用`,
                    disabled: true,
                  }))
                ] : [])
              ]}
              placeholder="请选择一个模型"
              disabled={disabled || availableModels.length === 0}
            />
          </div>
          
          {/* 选中模型信息 */}
          {selectedModelInfo && (
            <div className="p-4 bg-gray-50 rounded-lg space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">
                  {selectedModelInfo.name}
                </h4>
                <div className="flex items-center gap-2">
                  {getModelTypeBadge(selectedModelInfo.type)}
                  {selectedModelInfo.isAvailable ? (
                    <Badge variant="success" className="flex items-center gap-1">
                      <CheckCircle className="w-3 h-3" />
                      可用
                    </Badge>
                  ) : (
                    <Badge variant="danger" className="flex items-center gap-1">
                      <XCircle className="w-3 h-3" />
                      不可用
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">提供商:</span>
                  <span className="ml-2">{selectedModelInfo.provider}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">类型:</span>
                  <span className="ml-2">{selectedModelInfo.type}</span>
                </div>
              </div>
              
              {selectedModelInfo.description && (
                <div>
                  <span className="font-medium text-gray-600">描述:</span>
                  <p className="mt-1 text-sm text-gray-700">
                    {selectedModelInfo.description}
                  </p>
                </div>
              )}
            </div>
          )}
          
          {/* 模型统计 */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {modelArray.length}
              </div>
              <div className="text-sm text-blue-600">总模型数</div>
            </div>
            
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {availableModels.length}
              </div>
              <div className="text-sm text-green-600">可用模型</div>
            </div>
            
            <div className="p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {modelArray.filter(m => m.type === 'multimodal').length}
              </div>
              <div className="text-sm text-orange-600">多模态模型</div>
            </div>
          </div>
          
          {/* 提示信息 */}
          {availableModels.length === 0 && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                ⚠️ 当前没有可用的模型，请检查服务器配置
              </p>
            </div>
          )}
          
          {!selectedModel && availableModels.length > 0 && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-800">
                💡 请选择一个模型以开始分析
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
