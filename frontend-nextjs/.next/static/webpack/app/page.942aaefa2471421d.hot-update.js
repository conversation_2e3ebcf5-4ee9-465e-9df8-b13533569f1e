"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AudioControls__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AudioControls */ \"(app-pages-browser)/./src/components/AudioControls.tsx\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ModelSelector */ \"(app-pages-browser)/./src/components/ModelSelector.tsx\");\n/* harmony import */ var _components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TranscriptionHistory */ \"(app-pages-browser)/./src/components/TranscriptionHistory.tsx\");\n/* harmony import */ var _components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AnalysisResults */ \"(app-pages-browser)/./src/components/AnalysisResults.tsx\");\n/* harmony import */ var _components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MultimodalAnalysis */ \"(app-pages-browser)/./src/components/MultimodalAnalysis.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/store/useAppStore */ \"(app-pages-browser)/./src/store/useAppStore.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _services_websocket__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/websocket */ \"(app-pages-browser)/./src/services/websocket.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    // Canvas引用用于视频显示\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 状态管理\n    const { videoSessions, currentSessionId, audioStatus, transcriptionHistory, availableModels, selectedModel, analysisResults, isLoading, error, wsConnected, addVideoSession, removeVideoSession, setCurrentSession, setAudioStatus, addTranscriptionResult, clearTranscriptionHistory, setAvailableModels, setSelectedModel, addAnalysisResult, clearAnalysisResults, setLoading, setError, setWsConnected, getCurrentSession, getAudioStatus, getTranscriptionHistory } = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore)();\n    // 本地状态\n    const [rtspUrl, setRtspUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rtsp://*************:554/12');\n    const [audioEnabled, setAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentSession = getCurrentSession();\n    const currentAudioStatus = currentSessionId ? getAudioStatus(currentSessionId) : null;\n    const currentTranscriptionHistory = currentSessionId ? getTranscriptionHistory(currentSessionId) : [];\n    // WebSocket 消息处理\n    const handleWebSocketMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[handleWebSocketMessage]\": (message)=>{\n            if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isTranscriptionMessage)(message)) {\n                if (message.sessionId) {\n                    addTranscriptionResult(message.sessionId, message.result);\n                }\n            } else if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isMultimodalAnalysisMessage)(message)) {\n                addAnalysisResult(message.result);\n            }\n        }\n    }[\"HomePage.useCallback[handleWebSocketMessage]\"], [\n        addTranscriptionResult,\n        addAnalysisResult\n    ]);\n    // 视频帧渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            if (!currentSession || !canvasRef.current) return;\n            const canvas = canvasRef.current;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            let animationId;\n            const drawFrame = {\n                \"HomePage.useEffect.drawFrame\": ()=>{\n                    const time = Date.now() / 1000;\n                    // 创建动态背景\n                    const gradient = ctx.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, Math.max(canvas.width, canvas.height) / 2);\n                    gradient.addColorStop(0, '#1e3a8a');\n                    gradient.addColorStop(0.5, '#1e40af');\n                    gradient.addColorStop(1, '#1e293b');\n                    ctx.fillStyle = gradient;\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    // 添加动态粒子效果\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';\n                    for(let i = 0; i < 20; i++){\n                        const x = (Math.sin(time * 0.5 + i) * 0.3 + 0.5) * canvas.width;\n                        const y = (Math.cos(time * 0.3 + i * 0.5) * 0.3 + 0.5) * canvas.height;\n                        const size = Math.sin(time * 2 + i) * 2 + 3;\n                        ctx.beginPath();\n                        ctx.arc(x, y, size, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // 添加扫描线效果\n                    const scanLine = time * 100 % canvas.height;\n                    ctx.fillStyle = 'rgba(0, 255, 0, 0.1)';\n                    ctx.fillRect(0, scanLine, canvas.width, 2);\n                    // 添加中心信息面板\n                    const panelWidth = 400;\n                    const panelHeight = 120;\n                    const panelX = (canvas.width - panelWidth) / 2;\n                    const panelY = (canvas.height - panelHeight) / 2;\n                    // 半透明背景\n                    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';\n                    ctx.fillRect(panelX, panelY, panelWidth, panelHeight);\n                    // 边框\n                    ctx.strokeStyle = 'rgba(34, 197, 94, 0.8)';\n                    ctx.lineWidth = 2;\n                    ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);\n                    // 标题\n                    ctx.fillStyle = '#22c55e';\n                    ctx.font = 'bold 20px Arial';\n                    ctx.textAlign = 'center';\n                    ctx.fillText('RTSP 实时视频流', canvas.width / 2, panelY + 35);\n                    // 信息文本\n                    ctx.font = '14px Arial';\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';\n                    ctx.fillText(\"会话ID: \".concat(currentSession.id.slice(0, 8), \"...\"), canvas.width / 2, panelY + 60);\n                    ctx.fillText(\"时间: \".concat(new Date().toLocaleTimeString()), canvas.width / 2, panelY + 80);\n                    // 状态指示器\n                    const pulse = (Math.sin(time * 3) + 1) / 2;\n                    ctx.fillStyle = \"rgba(34, 197, 94, \".concat(0.5 + pulse * 0.5, \")\");\n                    ctx.beginPath();\n                    ctx.arc(canvas.width / 2 - 80, panelY + 95, 6, 0, Math.PI * 2);\n                    ctx.fill();\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';\n                    ctx.font = '12px Arial';\n                    ctx.textAlign = 'left';\n                    ctx.fillText('● LIVE', canvas.width / 2 - 70, panelY + 100);\n                    animationId = requestAnimationFrame(drawFrame);\n                }\n            }[\"HomePage.useEffect.drawFrame\"];\n            drawFrame();\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    if (animationId) {\n                        cancelAnimationFrame(animationId);\n                    }\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        currentSession\n    ]);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // 连接 WebSocket\n            _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.connect();\n            // 添加消息处理器\n            const removeMessageHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addMessageHandler(handleWebSocketMessage);\n            // 添加连接状态处理器\n            const removeConnectionHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addConnectionHandler({\n                \"HomePage.useEffect.removeConnectionHandler\": (status)=>{\n                    setWsConnected(status === 'connected');\n                }\n            }[\"HomePage.useEffect.removeConnectionHandler\"]);\n            // 加载可用模型\n            loadModels();\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    removeMessageHandler();\n                    removeConnectionHandler();\n                    _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.disconnect();\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        handleWebSocketMessage,\n        setWsConnected\n    ]);\n    // 加载模型列表\n    const loadModels = async ()=>{\n        try {\n            const models = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.getAvailableModels();\n            // 确保 models 是数组\n            const modelArray = Array.isArray(models) ? models : [];\n            setAvailableModels(modelArray);\n            // 自动选择第一个可用的多模态模型\n            const multimodalModel = modelArray.find((m)=>m.type === 'multimodal' && m.isAvailable);\n            if (multimodalModel && !selectedModel) {\n                setSelectedModel(multimodalModel.id);\n            }\n        } catch (error) {\n            console.error('Failed to load models:', error);\n            setError('加载模型列表失败');\n            // 设置默认的模型数据以便测试\n            setAvailableModels([\n                {\n                    id: 'test-multimodal',\n                    name: '测试多模态模型',\n                    type: 'multimodal',\n                    provider: 'Test',\n                    description: '用于测试的模拟模型',\n                    isAvailable: true\n                },\n                {\n                    id: 'test-vision',\n                    name: '测试视觉模型',\n                    type: 'vision',\n                    provider: 'Test',\n                    description: '用于测试的视觉模型',\n                    isAvailable: true\n                }\n            ]);\n        }\n    };\n    // 打开视频流\n    const handleOpenVideo = async ()=>{\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.isValidRtspUrl)(rtspUrl)) {\n            setError('请输入有效的RTSP地址');\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        console.log('开始打开视频流:', rtspUrl);\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.openVideo({\n                rtsp_url: rtspUrl,\n                enable_audio: audioEnabled\n            });\n            console.log('API响应:', response);\n            if (response.success) {\n                const session = {\n                    id: response.session_id,\n                    rtspUrl,\n                    isActive: true,\n                    audioEnabled,\n                    createdAt: new Date()\n                };\n                console.log('创建会话:', session);\n                addVideoSession(session);\n                setCurrentSession(response.session_id);\n                // 设置音频状态\n                if (response.audio_status) {\n                    setAudioStatus(response.session_id, response.audio_status);\n                }\n                console.log('视频流打开成功');\n            } else {\n                console.error('API返回失败:', response);\n                setError(response.message || '打开视频流失败');\n            }\n        } catch (error) {\n            console.error('API调用失败:', error);\n            setError('连接视频流失败，请检查网络和RTSP地址');\n            // 为了测试，我们可以创建一个模拟会话\n            console.log('创建模拟会话进行测试');\n            const mockSession = {\n                id: \"mock-\".concat(Date.now()),\n                rtspUrl,\n                isActive: true,\n                audioEnabled,\n                createdAt: new Date()\n            };\n            addVideoSession(mockSession);\n            setCurrentSession(mockSession.id);\n            setError('使用模拟视频流（后端连接失败）');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 关闭视频流\n    const handleCloseVideo = async ()=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.closeVideo(currentSessionId);\n            removeVideoSession(currentSessionId);\n            setCurrentSession(null);\n        } catch (error) {\n            console.error('Failed to close video:', error);\n            setError('关闭视频流失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 开始音频转录\n    const handleStartTranscription = async (language)=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.startAudioTranscription({\n                session_id: currentSessionId,\n                language\n            });\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: true,\n                    language\n                });\n            }\n        } catch (error) {\n            setError('启动音频转录失败');\n        }\n    };\n    // 停止音频转录\n    const handleStopTranscription = async ()=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.stopAudioTranscription(currentSessionId);\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: false\n                });\n            }\n        } catch (error) {\n            setError('停止音频转录失败');\n        }\n    };\n    // 多模态分析\n    const handleMultimodalAnalysis = async (prompt, includeAudio, contextSeconds)=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.analyzeMultimodal({\n                session_id: currentSessionId,\n                prompt,\n                include_recent_audio: includeAudio,\n                audio_context_seconds: contextSeconds\n            });\n            addAnalysisResult(result);\n        } catch (error) {\n            setError('多模态分析失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"VA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"视频分析系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                        variant: wsConnected ? \"success\" : \"danger\",\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            wsConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 32\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 63\n                                            }, this),\n                                            wsConnected ? '已连接' : '未连接'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        size: \"sm\",\n                                        variant: \"ghost\",\n                                        icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-5 h-5 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>setError(null),\n                                className: \"ml-auto\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    children: \"视频流控制\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        !currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium text-blue-900\",\n                                                                            children: \"第一步：打开视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                                    children: '输入RTSP地址，选择是否启用音频，然后点击\"打开视频流\"按钮'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    label: \"RTSP地址\",\n                                                                    value: rtspUrl,\n                                                                    onChange: (e)=>setRtspUrl(e.target.value),\n                                                                    placeholder: \"rtsp://*************:554/12\",\n                                                                    disabled: !!currentSession\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-end gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center gap-2 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: audioEnabled,\n                                                                                onChange: (e)=>setAudioEnabled(e.target.checked),\n                                                                                disabled: !!currentSession,\n                                                                                className: \"rounded\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"启用音频处理\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: !currentSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleOpenVideo,\n                                                                loading: isLoading,\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"打开视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleCloseVideo,\n                                                                loading: isLoading,\n                                                                variant: \"danger\",\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"关闭视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                            children: \"实时视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"RTSP地址: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-mono text-xs\",\n                                                                                    children: currentSession.rtspUrl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 535,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 534,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative bg-black rounded-lg overflow-hidden aspect-video border-2 border-gray-300 shadow-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                                                            ref: canvasRef,\n                                                                            width: 1280,\n                                                                            height: 720,\n                                                                            className: \"w-full h-full object-contain\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"success\",\n                                                                                className: \"bg-green-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 554,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"实时播放\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 550,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        currentSession.audioEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 right-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"primary\",\n                                                                                className: \"bg-blue-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 566,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"音频已启用\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 562,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 right-4 flex gap-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                                size: \"sm\",\n                                                                                variant: \"secondary\",\n                                                                                className: \"bg-black bg-opacity-50 text-white border-gray-600 hover:bg-opacity-70\",\n                                                                                onClick: ()=>{\n                                                                                    const canvas = canvasRef.current;\n                                                                                    if (canvas) {\n                                                                                        const link = document.createElement('a');\n                                                                                        link.download = \"screenshot-\".concat(Date.now(), \".png\");\n                                                                                        link.href = canvas.toDataURL();\n                                                                                        link.click();\n                                                                                    }\n                                                                                },\n                                                                                children: \"截图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 574,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-black bg-opacity-70 text-white px-3 py-2 rounded text-sm shadow-md\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"flex items-center gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                                    lineNumber: 597,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                \"会话: \",\n                                                                                                currentSession.id.slice(0, 8),\n                                                                                                \"...\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 596,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"1280x720\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 600,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: new Date().toLocaleTimeString()\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 601,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 595,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 594,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, this),\n                                    currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-blue-900\",\n                                                                children: \"视频流已打开，现在可以进行智能分析\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                        children: '请输入分析提示词或选择预设提示词，然后点击\"开始分析\"按钮'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__.MultimodalAnalysis, {\n                                                sessionId: currentSessionId,\n                                                audioEnabled: currentSession.audioEnabled || false,\n                                                onAnalyze: handleMultimodalAnalysis,\n                                                disabled: false,\n                                                loading: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 15\n                                    }, this),\n                                    analysisResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-green-50 border border-green-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-green-900\",\n                                                                children: \"分析结果\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700 mt-2 ml-8\",\n                                                        children: \"大模型实时输出的分析结果如下\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__.AnalysisResults, {\n                                                results: analysisResults,\n                                                onClear: clearAnalysisResults\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__.ModelSelector, {\n                                        models: availableModels,\n                                        selectedModel: selectedModel,\n                                        onModelSelect: setSelectedModel\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 13\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && currentAudioStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioControls__WEBPACK_IMPORTED_MODULE_2__.AudioControls, {\n                                        sessionId: currentSession.id,\n                                        audioStatus: currentAudioStatus,\n                                        onStartTranscription: handleStartTranscription,\n                                        onStopTranscription: handleStopTranscription,\n                                        onLanguageChange: ()=>{}\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 15\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__.TranscriptionHistory, {\n                                        history: currentTranscriptionHistory,\n                                        onClear: ()=>currentSessionId && clearTranscriptionHistory(currentSessionId)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 667,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n        lineNumber: 406,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"Vf8RV70pf4zhRU52G15SAYi/jmE=\", false, function() {\n    return [\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});