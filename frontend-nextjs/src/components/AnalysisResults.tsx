'use client';

import React, { useState } from 'react';
import { <PERSON>, CardH<PERSON>er, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { 
  Brain, 
  Trash2, 
  Copy, 
  Download,
  Clock,
  Eye,
  Volume2,
  Zap
} from 'lucide-react';
import { formatTimestamp, copyToClipboard, downloadAsJson, formatDuration } from '@/lib/utils';
import type { AnalysisResultsProps, MultimodalAnalysisResult } from '@/types';

export function AnalysisResults({
  results,
  onClear,
  maxResults = 10,
}: AnalysisResultsProps) {
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [expandedId, setExpandedId] = useState<string | null>(null);

  const displayResults = results.slice(0, maxResults);

  const handleCopy = async (text: string, id: string) => {
    const success = await copyToClipboard(text);
    if (success) {
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    }
  };

  const handleDownload = () => {
    const data = {
      exportTime: new Date().toISOString(),
      totalResults: results.length,
      analysisResults: results,
    };
    downloadAsJson(data, `analysis-results-${Date.now()}.json`);
  };

  const toggleExpanded = (id: string) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const getProcessingTimeColor = (time: number) => {
    if (time < 1000) return 'text-green-600';
    if (time < 3000) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            分析结果
            {results.length > 0 && (
              <Badge variant="primary">{results.length}</Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {results.length > 0 && (
              <>
                <Button
                  size="sm"
                  variant="ghost"
                  icon={Download}
                  onClick={handleDownload}
                >
                  导出
                </Button>
                
                <Button
                  size="sm"
                  variant="ghost"
                  icon={Trash2}
                  onClick={onClear}
                >
                  清空
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {displayResults.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Brain className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p className="text-lg mb-2">暂无分析结果</p>
              <p className="text-sm">执行多模态分析后，结果将显示在这里</p>
            </div>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {displayResults.map((result) => (
                <div
                  key={result.id}
                  className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                >
                  {/* 结果头部 */}
                  <div className="p-4 bg-gray-50 border-b">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="primary" className="flex items-center gap-1">
                          <Brain className="w-3 h-3" />
                          多模态分析
                        </Badge>
                        
                        {result.audioContext && (
                          <Badge variant="success" className="flex items-center gap-1">
                            <Volume2 className="w-3 h-3" />
                            含音频
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => toggleExpanded(result.id)}
                        >
                          {expandedId === result.id ? '收起' : '展开'}
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="ghost"
                          icon={Copy}
                          onClick={() => handleCopy(result.combinedAnalysis, result.id)}
                        >
                          {copiedId === result.id ? '已复制' : '复制'}
                        </Button>
                      </div>
                    </div>
                    
                    <div className="text-sm font-medium text-gray-700 mb-1">
                      提示词: {result.prompt}
                    </div>
                    
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {formatTimestamp(result.timestamp)}
                      </div>
                      
                      <div className={`flex items-center gap-1 ${getProcessingTimeColor(result.processingTime)}`}>
                        <Zap className="w-3 h-3" />
                        {formatDuration(result.processingTime / 1000)}
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3" />
                        会话: {result.sessionId}
                      </div>
                    </div>
                  </div>
                  
                  {/* 结果内容 */}
                  <div className="p-4">
                    {/* 综合分析结果 */}
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-900 mb-2">综合分析结果</h4>
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                          {result.combinedAnalysis}
                        </p>
                      </div>
                    </div>
                    
                    {/* 展开的详细信息 */}
                    {expandedId === result.id && (
                      <div className="space-y-4 border-t pt-4">
                        {/* 视觉分析 */}
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-1">
                            <Eye className="w-4 h-4" />
                            视觉分析
                          </h4>
                          <div className="p-3 bg-green-50 rounded-lg">
                            <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                              {result.visualAnalysis}
                            </p>
                          </div>
                        </div>
                        
                        {/* 音频上下文 */}
                        {result.audioContext && (
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-1">
                              <Volume2 className="w-4 h-4" />
                              音频上下文
                            </h4>
                            <div className="p-3 bg-yellow-50 rounded-lg">
                              <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                                {result.audioContext}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* 显示更多提示 */}
          {results.length > maxResults && (
            <div className="text-center py-2 text-sm text-gray-500 border-t">
              显示最近 {maxResults} 条结果，共 {results.length} 条
            </div>
          )}
          
          {/* 统计信息 */}
          {results.length > 0 && (
            <div className="grid grid-cols-3 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {results.length}
                </div>
                <div className="text-sm text-gray-500">总分析数</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {results.filter(r => r.audioContext).length}
                </div>
                <div className="text-sm text-gray-500">含音频分析</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {(results.reduce((sum, r) => sum + r.processingTime, 0) / results.length / 1000).toFixed(1)}s
                </div>
                <div className="text-sm text-gray-500">平均耗时</div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
