/** @type {import('next').NextConfig} */
const nextConfig = {
  // Next.js 15 中 appDir 已经是默认启用的，不需要在 experimental 中配置
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:8000/:path*',
      },
      // WebSocket 代理需要在开发服务器层面处理，不能通过 rewrites
      // 我们将在客户端直接连接到 WebSocket 服务器
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8000',
        pathname: '/**',
      },
    ],
    unoptimized: true,
  },
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    };
    return config;
  },
};

module.exports = nextConfig;
