'use client';

import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { 
  MessageSquare, 
  Trash2, 
  Copy, 
  Download,
  Clock,
  Volume2
} from 'lucide-react';
import { formatTimestamp, copyToClipboard, downloadAsJson } from '@/lib/utils';
import { LANGUAGE_OPTIONS } from '@/lib/config';
import type { TranscriptionHistoryProps, TranscriptionResult } from '@/types';

export function TranscriptionHistory({
  history,
  onClear,
  maxItems = 20,
}: TranscriptionHistoryProps) {
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const displayHistory = history.slice(0, maxItems);

  const handleCopy = async (text: string, id: string) => {
    const success = await copyToClipboard(text);
    if (success) {
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    }
  };

  const handleDownload = () => {
    const data = {
      exportTime: new Date().toISOString(),
      totalRecords: history.length,
      transcriptions: history,
    };
    downloadAsJson(data, `transcription-history-${Date.now()}.json`);
  };

  const getLanguageInfo = (language: string) => {
    const option = LANGUAGE_OPTIONS.find(opt => opt.value === language);
    return option ? `${option.flag} ${option.label}` : language;
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            转录历史
            {history.length > 0 && (
              <Badge variant="primary">{history.length}</Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {history.length > 0 && (
              <>
                <Button
                  size="sm"
                  variant="ghost"
                  icon={Download}
                  onClick={handleDownload}
                >
                  导出
                </Button>
                
                <Button
                  size="sm"
                  variant="ghost"
                  icon={Trash2}
                  onClick={onClear}
                >
                  清空
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {displayHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Volume2 className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p className="text-lg mb-2">暂无转录记录</p>
              <p className="text-sm">开始音频转录后，结果将显示在这里</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {displayHistory.map((item) => (
                <div
                  key={item.id}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {/* 转录文本 */}
                  <div className="mb-3">
                    <p className="text-gray-900 leading-relaxed">
                      {item.text}
                    </p>
                  </div>
                  
                  {/* 元信息 */}
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {formatTimestamp(item.timestamp)}
                      </div>
                      
                      <div>
                        语言: {getLanguageInfo(item.language)}
                      </div>
                      
                      <div className={`font-medium ${getConfidenceColor(item.confidence)}`}>
                        置信度: {(item.confidence * 100).toFixed(1)}%
                      </div>
                      
                      {item.duration && (
                        <div>
                          时长: {item.duration.toFixed(1)}s
                        </div>
                      )}
                    </div>
                    
                    <Button
                      size="sm"
                      variant="ghost"
                      icon={Copy}
                      onClick={() => handleCopy(item.text, item.id)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      {copiedId === item.id ? '已复制' : '复制'}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* 显示更多提示 */}
          {history.length > maxItems && (
            <div className="text-center py-2 text-sm text-gray-500 border-t">
              显示最近 {maxItems} 条记录，共 {history.length} 条
            </div>
          )}
          
          {/* 统计信息 */}
          {history.length > 0 && (
            <div className="grid grid-cols-3 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {history.length}
                </div>
                <div className="text-sm text-gray-500">总记录数</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {(history.reduce((sum, item) => sum + item.confidence, 0) / history.length * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-500">平均置信度</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {history.reduce((sum, item) => sum + (item.duration || 0), 0).toFixed(1)}s
                </div>
                <div className="text-sm text-gray-500">总时长</div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
