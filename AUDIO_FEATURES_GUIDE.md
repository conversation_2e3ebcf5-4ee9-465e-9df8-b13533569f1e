# 音频功能使用指南

## 概述

本系统现已支持从IP摄像头同时获取音频和视频，并提供语音转录和多模态分析功能。

## 功能特性

### 1. 音频处理
- **实时音频提取**: 从RTSP流中提取音频数据
- **语音转录**: 使用SenseVoice模型进行中文语音识别
- **多语言支持**: 支持中文、英文、粤语、日语、韩语等
- **实时处理**: 5秒音频片段实时转录

### 2. 多模态分析
- **视频+音频分析**: 结合视觉内容和音频转录进行综合分析
- **上下文感知**: 可配置音频上下文时长（5-300秒）
- **智能提示**: 自动构建包含音频信息的分析提示

### 3. WebSocket实时通信
- **转录结果推送**: 实时推送语音转录结果
- **多模态结果**: 实时推送多模态分析结果
- **状态同步**: 音频处理状态实时同步

## 使用方法

### 前端操作

1. **启用音频处理**
   - 在视频流控制面板中勾选"启用音频处理"
   - 打开视频时会同时启用音频功能

2. **语音转录控制**
   - 选择转录语言（自动检测/中文/英文等）
   - 点击"开始转录"启动实时语音转录
   - 查看转录历史记录
   - 点击"停止转录"结束转录

3. **多模态分析**
   - 配置音频上下文时长
   - 点击"多模态分析"进行视频+音频综合分析
   - 结果会包含视觉和听觉信息

### API接口

#### 音频转录
```bash
# 启动转录
POST /audio/transcription/start
{
    "session_id": "your_session_id",
    "language": "auto"  # auto/zh/en/yue/ja/ko
}

# 停止转录
POST /audio/transcription/stop/{session_id}

# 获取转录历史
GET /audio/transcription/history/{session_id}?limit=20
```

#### 多模态分析
```bash
POST /multimodal/analyze
{
    "session_id": "your_session_id",
    "prompt": "分析提示词",
    "include_recent_audio": true,
    "audio_context_seconds": 30
}
```

#### 音频状态
```bash
GET /audio/status/{session_id}
```

## 技术架构

### 音频服务组件

1. **SenseVoiceTranscriber**: 语音转录核心
   - 基于FunASR框架
   - 支持多语言识别
   - 实时转录处理

2. **AudioExtractor**: 音频提取器
   - FFmpeg音频流提取
   - PCM 16kHz单声道格式
   - 5秒片段分割

3. **AudioStreamService**: 音频流管理
   - 多会话管理
   - 转录回调处理
   - 资源清理

### 集成架构

```
RTSP摄像头 → VideoService (视频) + AudioService (音频)
                    ↓
            ModelManager (多模态分析)
                    ↓
            WebSocket (实时推送) → 前端显示
```

## 配置要求

### 依赖安装
```bash
# 安装FunASR (语音识别)
pip install funasr

# 安装FFmpeg (音频处理)
# macOS: brew install ffmpeg
# Ubuntu: apt-get install ffmpeg
```

### 模型下载
SenseVoice模型会在首次使用时自动下载到 `models/SenseVoiceSmall` 目录。

### 硬件要求
- **CPU**: 推荐4核以上
- **内存**: 推荐8GB以上
- **存储**: 模型文件约1GB

## 故障排除

### 常见问题

1. **音频转录不工作**
   - 检查FFmpeg是否正确安装
   - 确认RTSP流包含音频数据
   - 查看后端日志中的错误信息

2. **转录结果为空**
   - 检查音频质量和音量
   - 尝试不同的语言设置
   - 确认环境噪音不会过大

3. **多模态分析失败**
   - 确认视频和音频都正常工作
   - 检查模型管理器配置
   - 查看分析提示词是否合适

### 调试方法

1. **运行测试脚本**
   ```bash
   python test_audio_integration.py
   ```

2. **查看日志**
   - 后端日志: 检查音频服务和转录状态
   - 前端控制台: 查看WebSocket消息和错误

3. **检查音频文件**
   - 临时音频文件位置: `temp/audio_*`
   - 确认音频格式: PCM 16kHz mono

## 性能优化

### 音频处理优化
- 调整音频片段长度（默认5秒）
- 配置合适的音频上下文时长
- 定期清理转录历史记录

### 资源管理
- 音频临时文件自动清理
- 转录历史限制50条记录
- 会话结束时释放所有资源

## 扩展功能

### 未来计划
- 支持更多语音识别模型
- 音频情感分析
- 声纹识别
- 音频事件检测

### 自定义开发
- 扩展转录回调函数
- 添加音频预处理
- 集成其他语音服务

## 示例代码

### Python后端集成
```python
from services.audio_service import audio_service
from services.video_service import VideoStreamService

# 创建视频会话并启用音频
video_service = VideoStreamService()
await video_service.create_video_session(
    session_id="test",
    rtsp_url="rtsp://camera_ip:554/stream",
    enable_audio=True
)

# 启动转录
await video_service.start_audio_transcription("test", "zh")

# 多模态分析
result = await video_service.analyze_multimodal_frame(
    session_id="test",
    prompt="描述画面和声音",
    include_recent_audio=True
)
```

### JavaScript前端集成
```javascript
// 启用音频
setVideoState(prev => ({ ...prev, audioEnabled: true }));

// 开始转录
const startTranscription = async () => {
    const response = await fetch('/audio/transcription/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            session_id: sessionId,
            language: 'auto'
        })
    });
};

// 处理转录结果
const handleWebSocketMessage = (message) => {
    if (message.type === 'transcription_result') {
        console.log('转录结果:', message.result.text);
    }
};
```

## 联系支持

如有问题或建议，请查看项目文档或提交Issue。
