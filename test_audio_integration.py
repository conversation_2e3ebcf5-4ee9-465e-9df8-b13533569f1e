#!/usr/bin/env python3
"""
音频集成测试脚本
测试音频服务、视频服务和多模态分析的集成功能
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.audio_service import audio_service
from services.video_service import VideoStreamService
from models.model_manager import ModelManager
from utils.websocket_manager import WebSocketManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AudioIntegrationTester:
    """音频集成测试器"""
    
    def __init__(self):
        self.video_service = VideoStreamService()
        self.model_manager = ModelManager()
        self.websocket_manager = WebSocketManager()
        self.test_session_id = "test_audio_session"
        self.test_rtsp_url = "rtsp://192.168.1.109:554/12"  # 测试RTSP地址
        
    async def test_audio_service_initialization(self):
        """测试音频服务初始化"""
        logger.info("=== 测试音频服务初始化 ===")
        
        try:
            # 检查音频服务是否可用
            if not audio_service.is_available():
                logger.warning("音频服务不可用，可能缺少依赖")
                return False
                
            logger.info("✓ 音频服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"✗ 音频服务初始化失败: {e}")
            return False
    
    async def test_video_audio_integration(self):
        """测试视频和音频集成"""
        logger.info("=== 测试视频音频集成 ===")
        
        try:
            # 创建视频会话并启用音频
            success = await self.video_service.create_video_session(
                session_id=self.test_session_id,
                rtsp_url=self.test_rtsp_url,
                model_manager=self.model_manager,
                websocket_manager=self.websocket_manager,
                enable_audio=True
            )
            
            if not success:
                logger.error("✗ 创建视频会话失败")
                return False
                
            logger.info("✓ 视频会话创建成功，音频已启用")
            
            # 检查音频状态
            audio_status = self.video_service.get_audio_status(self.test_session_id)
            logger.info(f"音频状态: {audio_status}")
            
            # 清理
            await self.video_service.close_video_session(self.test_session_id)
            logger.info("✓ 视频会话已关闭")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ 视频音频集成测试失败: {e}")
            return False
    
    async def test_audio_transcription(self):
        """测试音频转录功能"""
        logger.info("=== 测试音频转录功能 ===")
        
        try:
            # 创建视频会话
            success = await self.video_service.create_video_session(
                session_id=self.test_session_id,
                rtsp_url=self.test_rtsp_url,
                model_manager=self.model_manager,
                websocket_manager=self.websocket_manager,
                enable_audio=True
            )
            
            if not success:
                logger.error("✗ 创建视频会话失败")
                return False
            
            # 启动音频转录
            transcription_success = await self.video_service.start_audio_transcription(
                self.test_session_id,
                language="auto"
            )
            
            if not transcription_success:
                logger.error("✗ 启动音频转录失败")
                return False
                
            logger.info("✓ 音频转录已启动")
            
            # 等待一段时间收集转录结果
            logger.info("等待10秒收集转录结果...")
            await asyncio.sleep(10)
            
            # 获取转录历史
            history = self.video_service.get_transcription_history(self.test_session_id, limit=5)
            logger.info(f"转录历史记录数: {len(history)}")
            
            for i, record in enumerate(history):
                logger.info(f"转录 {i+1}: {record.get('text', 'N/A')}")
            
            # 停止转录
            await self.video_service.stop_audio_transcription(self.test_session_id)
            logger.info("✓ 音频转录已停止")
            
            # 清理
            await self.video_service.close_video_session(self.test_session_id)
            
            return True
            
        except Exception as e:
            logger.error(f"✗ 音频转录测试失败: {e}")
            return False
    
    async def test_multimodal_analysis(self):
        """测试多模态分析功能"""
        logger.info("=== 测试多模态分析功能 ===")
        
        try:
            # 创建视频会话
            success = await self.video_service.create_video_session(
                session_id=self.test_session_id,
                rtsp_url=self.test_rtsp_url,
                model_manager=self.model_manager,
                websocket_manager=self.websocket_manager,
                enable_audio=True
            )
            
            if not success:
                logger.error("✗ 创建视频会话失败")
                return False
            
            # 启动音频转录
            await self.video_service.start_audio_transcription(
                self.test_session_id,
                language="auto"
            )
            
            # 等待收集一些音频数据
            logger.info("等待5秒收集音频数据...")
            await asyncio.sleep(5)
            
            # 执行多模态分析
            result = await self.video_service.analyze_multimodal_frame(
                session_id=self.test_session_id,
                prompt="请描述你看到的画面和听到的声音",
                include_recent_audio=True,
                audio_context_seconds=30
            )
            
            logger.info(f"多模态分析结果: {result.get('success', False)}")
            if result.get('success'):
                logger.info(f"分析文本: {result.get('text', 'N/A')}")
                logger.info(f"使用音频上下文: {result.get('audio_context_used', False)}")
                logger.info(f"音频上下文长度: {result.get('audio_context_length', 0)}")
            else:
                logger.error(f"分析失败: {result.get('error', 'N/A')}")
            
            # 清理
            await self.video_service.stop_audio_transcription(self.test_session_id)
            await self.video_service.close_video_session(self.test_session_id)
            
            return result.get('success', False)
            
        except Exception as e:
            logger.error(f"✗ 多模态分析测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始音频集成测试...")
        
        tests = [
            ("音频服务初始化", self.test_audio_service_initialization),
            ("视频音频集成", self.test_video_audio_integration),
            ("音频转录功能", self.test_audio_transcription),
            ("多模态分析功能", self.test_multimodal_analysis),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results[test_name] = result
                logger.info(f"{test_name}: {'✓ 通过' if result else '✗ 失败'}")
            except Exception as e:
                results[test_name] = False
                logger.error(f"{test_name}: ✗ 异常 - {e}")
            
            logger.info("-" * 50)
        
        # 总结
        logger.info("=== 测试总结 ===")
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✓ 通过" if result else "✗ 失败"
            logger.info(f"{test_name}: {status}")
        
        logger.info(f"总计: {passed}/{total} 测试通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！音频集成功能正常")
        else:
            logger.warning(f"⚠️  {total - passed} 个测试失败，请检查相关功能")
        
        return passed == total

async def main():
    """主函数"""
    tester = AudioIntegrationTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("音频集成测试完成，所有功能正常")
        sys.exit(0)
    else:
        logger.error("音频集成测试失败，存在问题需要修复")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
