import type { AppConfig } from '@/types';

export const config: AppConfig = {
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000',
  wsUrl: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws/frontend-client',
  maxTranscriptionHistory: 50,
  maxAnalysisResults: 20,
  audioContextSeconds: 30,
  reconnectInterval: 3000,
  maxReconnectAttempts: 5,
};

export const API_ENDPOINTS = {
  // 视频相关
  VIDEO_OPEN: '/video/open',
  VIDEO_CLOSE: '/video/close',
  VIDEO_ANALYZE: '/video/analyze',
  
  // 音频相关
  AUDIO_TRANSCRIPTION_START: '/audio/transcription/start',
  AUDIO_TRANSCRIPTION_STOP: '/audio/transcription/stop',
  AUDIO_STATUS: '/audio/status',
  AUDIO_HISTORY: '/audio/transcription/history',
  
  // 多模态分析
  MULTIMODAL_ANALYZE: '/multimodal/analyze',
  
  // 模型相关
  MODELS_LIST: '/models',
  MODEL_SELECT: '/model/select',
} as const;

export const LANGUAGE_OPTIONS = [
  { value: 'auto' as const, label: '自动检测', flag: '🌐' },
  { value: 'zh' as const, label: '中文', flag: '🇨🇳' },
  { value: 'en' as const, label: 'English', flag: '🇺🇸' },
  { value: 'yue' as const, label: '粤语', flag: '🇭🇰' },
  { value: 'ja' as const, label: '日本語', flag: '🇯🇵' },
  { value: 'ko' as const, label: '한국어', flag: '🇰🇷' },
  { value: 'es' as const, label: 'Español', flag: '🇪🇸' },
  { value: 'fr' as const, label: 'Français', flag: '🇫🇷' },
  { value: 'de' as const, label: 'Deutsch', flag: '🇩🇪' },
  { value: 'it' as const, label: 'Italiano', flag: '🇮🇹' },
  { value: 'pt' as const, label: 'Português', flag: '🇵🇹' },
  { value: 'ru' as const, label: 'Русский', flag: '🇷🇺' },
  { value: 'ar' as const, label: 'العربية', flag: '🇸🇦' },
  { value: 'hi' as const, label: 'हिन्दी', flag: '🇮🇳' },
  { value: 'th' as const, label: 'ไทย', flag: '🇹🇭' },
  { value: 'vi' as const, label: 'Tiếng Việt', flag: '🇻🇳' },
] as const;
