"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.4_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AudioControls__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AudioControls */ \"(app-pages-browser)/./src/components/AudioControls.tsx\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ModelSelector */ \"(app-pages-browser)/./src/components/ModelSelector.tsx\");\n/* harmony import */ var _components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TranscriptionHistory */ \"(app-pages-browser)/./src/components/TranscriptionHistory.tsx\");\n/* harmony import */ var _components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AnalysisResults */ \"(app-pages-browser)/./src/components/AnalysisResults.tsx\");\n/* harmony import */ var _components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MultimodalAnalysis */ \"(app-pages-browser)/./src/components/MultimodalAnalysis.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Play,Settings,Square,Volume2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/store/useAppStore */ \"(app-pages-browser)/./src/store/useAppStore.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _services_websocket__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/websocket */ \"(app-pages-browser)/./src/services/websocket.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    // 视频显示引用\n    const videoDisplayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 视频帧请求定时器\n    const frameRequestInterval = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 状态管理\n    const { videoSessions, currentSessionId, audioStatus, transcriptionHistory, availableModels, selectedModel, analysisResults, isLoading, error, wsConnected, addVideoSession, removeVideoSession, setCurrentSession, updateVideoFrame, setAudioStatus, addTranscriptionResult, clearTranscriptionHistory, setAvailableModels, setSelectedModel, addAnalysisResult, clearAnalysisResults, setLoading, setError, setWsConnected, getCurrentSession, getAudioStatus, getTranscriptionHistory } = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore)();\n    // 本地状态\n    const [rtspUrl, setRtspUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rtsp://*************:554/12');\n    const [audioEnabled, setAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentSession = getCurrentSession();\n    const currentAudioStatus = currentSessionId ? getAudioStatus(currentSessionId) : null;\n    // 调试信息\n    console.log('当前状态:', {\n        currentSessionId,\n        currentSession,\n        videoSessions,\n        hasCurrentSession: !!currentSession\n    });\n    const currentTranscriptionHistory = currentSessionId ? getTranscriptionHistory(currentSessionId) : [];\n    // WebSocket 消息处理\n    const handleWebSocketMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[handleWebSocketMessage]\": (message)=>{\n            // 添加详细的消息调试\n            console.log('收到WebSocket消息:', {\n                type: message.type,\n                keys: Object.keys(message),\n                messagePreview: JSON.stringify(message).substring(0, 200) + '...'\n            });\n            if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isTranscriptionMessage)(message)) {\n                if (message.sessionId) {\n                    addTranscriptionResult(message.sessionId, message.result);\n                }\n            } else if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isMultimodalAnalysisMessage)(message)) {\n                addAnalysisResult(message.result);\n            } else if ((0,_services_websocket__WEBPACK_IMPORTED_MODULE_13__.isVideoFrameMessage)(message)) {\n                var _message_frameData;\n                // 处理视频帧数据\n                console.log('✅ 确认收到视频帧消息:', {\n                    sessionId: message.session_id,\n                    frameDataLength: ((_message_frameData = message.frameData) === null || _message_frameData === void 0 ? void 0 : _message_frameData.length) || 0,\n                    currentSessionId,\n                    messageType: message.type\n                });\n                if (currentSessionId && message.frameData) {\n                    console.log('🎬 更新视频帧数据到store');\n                    updateVideoFrame(currentSessionId, message.frameData);\n                } else {\n                    console.warn('❌ 无法更新视频帧:', {\n                        currentSessionId,\n                        hasFrameData: !!message.frameData,\n                        messageSessionId: message.session_id\n                    });\n                }\n            } else {\n                console.log('🔍 未识别的消息类型:', message.type);\n            }\n        }\n    }[\"HomePage.useCallback[handleWebSocketMessage]\"], [\n        addTranscriptionResult,\n        addAnalysisResult,\n        updateVideoFrame,\n        currentSessionId\n    ]);\n    // 请求视频帧\n    const requestVideoFrame = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[requestVideoFrame]\": ()=>{\n            if (_services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.getConnectionStatus() === 'connected' && currentSessionId) {\n                _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.send({\n                    type: 'request_frame',\n                    session_id: currentSessionId,\n                    timestamp: new Date().toISOString()\n                });\n            }\n        }\n    }[\"HomePage.useCallback[requestVideoFrame]\"], [\n        currentSessionId\n    ]);\n    // 启动帧请求定时器\n    const startFrameRequesting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[startFrameRequesting]\": (sessionId)=>{\n            if (frameRequestInterval.current) {\n                clearInterval(frameRequestInterval.current);\n            }\n            // 立即请求第一帧\n            if (_services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.getConnectionStatus() === 'connected') {\n                _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.send({\n                    type: 'request_frame',\n                    session_id: sessionId,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            // 设置定时器每100毫秒请求一次帧\n            frameRequestInterval.current = setInterval({\n                \"HomePage.useCallback[startFrameRequesting]\": ()=>{\n                    if (_services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.getConnectionStatus() === 'connected' && currentSessionId === sessionId) {\n                        requestVideoFrame();\n                    } else {\n                        stopFrameRequesting();\n                    }\n                }\n            }[\"HomePage.useCallback[startFrameRequesting]\"], 100);\n            console.log('启动视频帧请求定时器 (100ms)');\n        }\n    }[\"HomePage.useCallback[startFrameRequesting]\"], [\n        requestVideoFrame,\n        currentSessionId\n    ]);\n    // 停止帧请求定时器\n    const stopFrameRequesting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[stopFrameRequesting]\": ()=>{\n            if (frameRequestInterval.current) {\n                clearInterval(frameRequestInterval.current);\n                frameRequestInterval.current = null;\n                console.log('停止视频帧请求定时器');\n            }\n        }\n    }[\"HomePage.useCallback[stopFrameRequesting]\"], []);\n    // 视频帧显示\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            var _currentSession_currentFrame;\n            console.log('🎥 视频帧显示useEffect触发:', {\n                hasCurrentSession: !!currentSession,\n                hasCurrentFrame: !!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.currentFrame),\n                frameLength: (currentSession === null || currentSession === void 0 ? void 0 : (_currentSession_currentFrame = currentSession.currentFrame) === null || _currentSession_currentFrame === void 0 ? void 0 : _currentSession_currentFrame.length) || 0,\n                hasVideoDisplayRef: !!videoDisplayRef.current\n            });\n            if (!currentSession) {\n                console.log('❌ 没有当前会话');\n                return;\n            }\n            // 如果有新的视频帧数据，直接显示\n            if (currentSession.currentFrame && videoDisplayRef.current) {\n                console.log('🖼️ 显示新视频帧，数据长度:', currentSession.currentFrame.length);\n                // 直接设置img标签的src为base64数据\n                videoDisplayRef.current.src = \"data:image/jpeg;base64,\".concat(currentSession.currentFrame);\n                videoDisplayRef.current.style.display = 'block';\n                console.log('✅ 视频帧已设置到img标签');\n            } else {\n                console.log('⏳ 等待视频帧数据...', {\n                    hasFrame: !!currentSession.currentFrame,\n                    hasRef: !!videoDisplayRef.current\n                });\n            }\n        }\n    }[\"HomePage.useEffect\"], [\n        currentSession === null || currentSession === void 0 ? void 0 : currentSession.currentFrame\n    ]);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // 连接 WebSocket\n            _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.connect();\n            // 添加消息处理器\n            const removeMessageHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addMessageHandler(handleWebSocketMessage);\n            // 添加连接状态处理器\n            const removeConnectionHandler = _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.addConnectionHandler({\n                \"HomePage.useEffect.removeConnectionHandler\": (status)=>{\n                    setWsConnected(status === 'connected');\n                }\n            }[\"HomePage.useEffect.removeConnectionHandler\"]);\n            // 加载可用模型\n            loadModels();\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    removeMessageHandler();\n                    removeConnectionHandler();\n                    stopFrameRequesting(); // 清理帧请求定时器\n                    _services_websocket__WEBPACK_IMPORTED_MODULE_13__.wsService.disconnect();\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        handleWebSocketMessage,\n        setWsConnected,\n        stopFrameRequesting\n    ]);\n    // 加载模型列表\n    const loadModels = async ()=>{\n        try {\n            const models = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.getAvailableModels();\n            // 确保 models 是数组\n            const modelArray = Array.isArray(models) ? models : [];\n            setAvailableModels(modelArray);\n            // 自动选择第一个可用的多模态模型\n            const multimodalModel = modelArray.find((m)=>m.type === 'multimodal' && m.isAvailable);\n            if (multimodalModel && !selectedModel) {\n                setSelectedModel(multimodalModel.id);\n            }\n        } catch (error) {\n            console.error('Failed to load models:', error);\n            setError('加载模型列表失败');\n            // 设置默认的模型数据以便测试\n            setAvailableModels([\n                {\n                    id: 'test-multimodal',\n                    name: '测试多模态模型',\n                    type: 'multimodal',\n                    provider: 'Test',\n                    description: '用于测试的模拟模型',\n                    isAvailable: true\n                },\n                {\n                    id: 'test-vision',\n                    name: '测试视觉模型',\n                    type: 'vision',\n                    provider: 'Test',\n                    description: '用于测试的视觉模型',\n                    isAvailable: true\n                }\n            ]);\n        }\n    };\n    // 打开视频流\n    const handleOpenVideo = async ()=>{\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.isValidRtspUrl)(rtspUrl)) {\n            setError('请输入有效的RTSP地址');\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        console.log('开始打开视频流:', rtspUrl);\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.openVideo({\n                rtsp_url: rtspUrl,\n                enable_audio: audioEnabled\n            });\n            console.log('API响应:', response);\n            if (response.success) {\n                const session = {\n                    id: response.session_id,\n                    rtspUrl,\n                    isActive: true,\n                    audioEnabled,\n                    createdAt: new Date()\n                };\n                console.log('创建会话:', session);\n                addVideoSession(session);\n                setCurrentSession(response.session_id);\n                // 设置音频状态\n                if (response.audio_status) {\n                    setAudioStatus(response.session_id, response.audio_status);\n                }\n                // 启动视频帧请求\n                startFrameRequesting(response.session_id);\n                console.log('视频流打开成功');\n            } else {\n                console.error('API返回失败:', response);\n                setError(response.message || '打开视频流失败');\n            }\n        } catch (error) {\n            console.error('API调用失败:', error);\n            setError('连接视频流失败，请检查网络和RTSP地址');\n            // 为了测试，我们可以创建一个模拟会话\n            console.log('创建模拟会话进行测试');\n            const mockSession = {\n                id: \"mock-\".concat(Date.now()),\n                rtspUrl,\n                isActive: true,\n                audioEnabled,\n                createdAt: new Date()\n            };\n            addVideoSession(mockSession);\n            setCurrentSession(mockSession.id);\n            // 即使是模拟会话也启动帧请求（用于测试）\n            startFrameRequesting(mockSession.id);\n            setError('使用模拟视频流（后端连接失败）');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 关闭视频流\n    const handleCloseVideo = async ()=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            // 停止帧请求\n            stopFrameRequesting();\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.closeVideo(currentSessionId);\n            removeVideoSession(currentSessionId);\n            setCurrentSession(null);\n        } catch (error) {\n            console.error('Failed to close video:', error);\n            setError('关闭视频流失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 开始音频转录\n    const handleStartTranscription = async (language)=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.startAudioTranscription({\n                session_id: currentSessionId,\n                language\n            });\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: true,\n                    language\n                });\n            }\n        } catch (error) {\n            setError('启动音频转录失败');\n        }\n    };\n    // 停止音频转录\n    const handleStopTranscription = async ()=>{\n        if (!currentSessionId) return;\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.stopAudioTranscription(currentSessionId);\n            // 更新音频状态\n            const currentStatus = getAudioStatus(currentSessionId);\n            if (currentStatus) {\n                setAudioStatus(currentSessionId, {\n                    ...currentStatus,\n                    transcriptionActive: false\n                });\n            }\n        } catch (error) {\n            setError('停止音频转录失败');\n        }\n    };\n    // 多模态分析\n    const handleMultimodalAnalysis = async (prompt, includeAudio, contextSeconds)=>{\n        if (!currentSessionId) return;\n        setLoading(true);\n        try {\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_12__.apiService.analyzeMultimodal({\n                session_id: currentSessionId,\n                prompt,\n                include_recent_audio: includeAudio,\n                audio_context_seconds: contextSeconds\n            });\n            addAnalysisResult(result);\n        } catch (error) {\n            setError('多模态分析失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"VA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"视频分析系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                        variant: wsConnected ? \"success\" : \"danger\",\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            wsConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 32\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 63\n                                            }, this),\n                                            wsConnected ? '已连接' : '未连接'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        size: \"sm\",\n                                        variant: \"ghost\",\n                                        icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-5 h-5 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>setError(null),\n                                className: \"ml-auto\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    children: \"视频流控制\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        !currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium text-blue-900\",\n                                                                            children: \"第一步：打开视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                                    children: '输入RTSP地址，选择是否启用音频，然后点击\"打开视频流\"按钮'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                    label: \"RTSP地址\",\n                                                                    value: rtspUrl,\n                                                                    onChange: (e)=>setRtspUrl(e.target.value),\n                                                                    placeholder: \"rtsp://*************:554/12\",\n                                                                    disabled: !!currentSession\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-end gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center gap-2 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: audioEnabled,\n                                                                                onChange: (e)=>setAudioEnabled(e.target.checked),\n                                                                                disabled: !!currentSession,\n                                                                                className: \"rounded\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 519,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"启用音频处理\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: !currentSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleOpenVideo,\n                                                                loading: isLoading,\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"打开视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                onClick: handleCloseVideo,\n                                                                loading: isLoading,\n                                                                variant: \"danger\",\n                                                                icon: _barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                                className: \"flex-1\",\n                                                                children: \"关闭视频流\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                            children: \"实时视频流\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"RTSP地址: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-mono text-xs\",\n                                                                                    children: currentSession.rtspUrl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 560,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative bg-black rounded-lg overflow-hidden aspect-video border-2 border-gray-300 shadow-lg\",\n                                                                    children: [\n                                                                        (currentSession === null || currentSession === void 0 ? void 0 : currentSession.currentFrame) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    ref: videoDisplayRef,\n                                                                                    alt: \"RTSP视频流\",\n                                                                                    className: \"w-full h-full object-contain\",\n                                                                                    onLoad: ()=>console.log('视频帧显示成功'),\n                                                                                    onError: (e)=>{\n                                                                                        var _currentSession_currentFrame;\n                                                                                        console.error('视频帧显示失败:', e);\n                                                                                        console.log('当前帧数据长度:', (currentSession === null || currentSession === void 0 ? void 0 : (_currentSession_currentFrame = currentSession.currentFrame) === null || _currentSession_currentFrame === void 0 ? void 0 : _currentSession_currentFrame.length) || 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute top-2 right-2 bg-black bg-opacity-70 text-white px-3 py-2 rounded-lg text-sm\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                                    lineNumber: 582,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-semibold\",\n                                                                                                    children: \"LIVE\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                                    lineNumber: 583,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 581,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-gray-300 mt-1\",\n                                                                                            children: [\n                                                                                                \"会话: \",\n                                                                                                currentSession.id.slice(0, 8),\n                                                                                                \"...\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 585,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 580,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full h-full flex items-center justify-center text-white\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 593,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-semibold\",\n                                                                                        children: \"等待视频流数据...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 594,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-400 mt-2\",\n                                                                                        children: \"请确保RTSP地址正确且网络连接正常\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 595,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"success\",\n                                                                                className: \"bg-green-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 606,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"实时播放\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        currentSession.audioEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-4 right-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                variant: \"primary\",\n                                                                                className: \"bg-blue-600 text-white flex items-center gap-1 shadow-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Play_Settings_Square_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                        lineNumber: 618,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"音频已启用\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 right-4 flex gap-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                                size: \"sm\",\n                                                                                variant: \"secondary\",\n                                                                                className: \"bg-black bg-opacity-50 text-white border-gray-600 hover:bg-opacity-70\",\n                                                                                onClick: ()=>{\n                                                                                    const img = videoDisplayRef.current;\n                                                                                    if (img && (currentSession === null || currentSession === void 0 ? void 0 : currentSession.currentFrame)) {\n                                                                                        // 创建Canvas来生成截图\n                                                                                        const canvas = document.createElement('canvas');\n                                                                                        const ctx = canvas.getContext('2d');\n                                                                                        if (ctx) {\n                                                                                            canvas.width = img.naturalWidth;\n                                                                                            canvas.height = img.naturalHeight;\n                                                                                            ctx.drawImage(img, 0, 0);\n                                                                                            const link = document.createElement('a');\n                                                                                            link.download = \"rtsp-screenshot-\".concat(Date.now(), \".png\");\n                                                                                            link.href = canvas.toDataURL();\n                                                                                            link.click();\n                                                                                        }\n                                                                                    }\n                                                                                },\n                                                                                children: \"截图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 626,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 625,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 left-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-black bg-opacity-70 text-white px-3 py-2 rounded text-sm shadow-md\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"flex items-center gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                                    lineNumber: 658,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                \"会话: \",\n                                                                                                currentSession.id.slice(0, 8),\n                                                                                                \"...\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 657,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"1280x720\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 661,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: new Date().toLocaleTimeString()\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                            lineNumber: 662,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                            lineNumber: 654,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, this),\n                                    currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-blue-900\",\n                                                                children: \"视频流已打开，现在可以进行智能分析\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700 mt-2 ml-8\",\n                                                        children: '请输入分析提示词或选择预设提示词，然后点击\"开始分析\"按钮'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MultimodalAnalysis__WEBPACK_IMPORTED_MODULE_6__.MultimodalAnalysis, {\n                                                sessionId: currentSessionId,\n                                                audioEnabled: currentSession.audioEnabled || false,\n                                                onAnalyze: handleMultimodalAnalysis,\n                                                disabled: false,\n                                                loading: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 15\n                                    }, this),\n                                    analysisResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-green-50 border border-green-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-green-900\",\n                                                                children: \"分析结果\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700 mt-2 ml-8\",\n                                                        children: \"大模型实时输出的分析结果如下\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__.AnalysisResults, {\n                                                results: analysisResults,\n                                                onClear: clearAnalysisResults\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__.ModelSelector, {\n                                        models: availableModels,\n                                        selectedModel: selectedModel,\n                                        onModelSelect: setSelectedModel\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 13\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && currentAudioStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioControls__WEBPACK_IMPORTED_MODULE_2__.AudioControls, {\n                                        sessionId: currentSession.id,\n                                        audioStatus: currentAudioStatus,\n                                        onStartTranscription: handleStartTranscription,\n                                        onStopTranscription: handleStopTranscription,\n                                        onLanguageChange: ()=>{}\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 15\n                                    }, this),\n                                    (currentSession === null || currentSession === void 0 ? void 0 : currentSession.audioEnabled) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TranscriptionHistory__WEBPACK_IMPORTED_MODULE_4__.TranscriptionHistory, {\n                                        history: currentTranscriptionHistory,\n                                        onClear: ()=>currentSessionId && clearTranscriptionHistory(currentSessionId)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                                lineNumber: 728,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Source/VLM/video-analysis-system/frontend-nextjs/src/app/page.tsx\",\n        lineNumber: 431,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"sWQEa9mAZY9X0NDtgNO5AYBHUIg=\", false, function() {\n    return [\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_11__.useAppStore\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});