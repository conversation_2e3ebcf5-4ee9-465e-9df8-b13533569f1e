# 视频分析系统 - Next.js 前端

基于 Next.js 15 + Tailwind CSS 的现代化视频分析系统前端，支持实时视频流处理、音频转录和多模态AI分析。

## 🚀 快速开始

### 环境要求

- Node.js 18+ 
- pnpm (推荐) 或 npm

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
pnpm build
pnpm start
```

## 📋 功能特性

### ✅ 核心功能

- **🎥 实时视频流**: 支持 RTSP 视频流播放和控制
- **🎤 音频转录**: 实时语音转文字，支持多种语言
- **🧠 多模态分析**: 结合视频和音频的 AI 智能分析
- **🔄 实时通信**: WebSocket 实时数据传输
- **📊 历史记录**: 转录历史和分析结果管理

### 🎨 UI/UX 特色

- **📱 响应式设计**: 完美适配桌面和移动设备
- **🌈 现代化界面**: 基于 Tailwind CSS 的设计系统
- **⚡ 流畅交互**: 丰富的动画和过渡效果
- **🎯 直观操作**: 清晰的状态指示和用户反馈

## 🏗️ 技术架构

### 核心技术栈

- **Next.js 15**: React 全栈框架，支持 App Router
- **TypeScript**: 完整类型安全保障
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Zustand**: 轻量级状态管理
- **Lucide React**: 现代化图标库

### 项目结构

```
src/
├── app/                          # Next.js App Router 页面
│   ├── globals.css               # 全局样式和组件样式
│   ├── layout.tsx                # 根布局组件
│   ├── page.tsx                  # 主页面
│   └── test/                     # 组件测试页面
├── components/                   # React 组件
│   ├── ui/                       # 基础 UI 组件
│   │   ├── Button.tsx            # 按钮组件
│   │   ├── Input.tsx             # 输入框组件
│   │   ├── Select.tsx            # 选择器组件
│   │   ├── Card.tsx              # 卡片组件
│   │   └── Badge.tsx             # 徽章组件
│   ├── VideoPlayer.tsx           # 视频播放器
│   ├── AudioControls.tsx         # 音频控制面板
│   ├── ModelSelector.tsx         # AI 模型选择器
│   ├── TranscriptionHistory.tsx  # 转录历史
│   ├── AnalysisResults.tsx       # 分析结果展示
│   └── MultimodalAnalysis.tsx    # 多模态分析控制
├── services/                     # 服务层
│   ├── api.ts                    # API 服务封装
│   └── websocket.ts              # WebSocket 服务
├── store/                        # 状态管理
│   └── useAppStore.ts            # Zustand 全局状态
├── types/                        # TypeScript 类型定义
│   └── index.ts                  # 所有类型定义
└── lib/                          # 工具函数
    ├── utils.ts                  # 通用工具函数
    └── config.ts         # 配置常量
```

## 🔧 核心组件

### VideoPlayer 组件
- 实时 RTSP 视频流播放
- 播放控制和状态管理
- 错误处理和重连机制

### AudioControls 组件
- 音频转录控制
- 多语言支持
- 实时状态显示

### ModelSelector 组件
- AI 模型选择和管理
- 模型状态监控
- 类型分类显示

### MultimodalAnalysis 组件
- 自定义分析提示词
- 音频上下文配置
- 预设快速选项

## 🛠️ 开发指南

### 状态管理

使用 Zustand 进行全局状态管理：

```typescript
const {
  videoSessions,
  currentSessionId,
  audioStatus,
  addVideoSession,
  setCurrentSession
} = useAppStore();
```

### API 调用

统一的 API 服务层：

```typescript
import { apiService } from '@/services/api';

// 打开视频流
const response = await apiService.openVideo({
  rtsp_url: 'rtsp://example.com/stream',
  enable_audio: true
});
```

### WebSocket 通信

实时数据通信：

```typescript
import { wsService } from '@/services/websocket';

// 添加消息处理器
wsService.addMessageHandler((message) => {
  if (isTranscriptionMessage(message)) {
    // 处理转录结果
  }
});
```

## 🎨 样式系统

### Tailwind CSS 配置

扩展的颜色主题：

```javascript
// tailwind.config.js
theme: {
  extend: {
    colors: {
      primary: { /* 主色调 */ },
      success: { /* 成功色 */ },
      warning: { /* 警告色 */ },
      danger: { /* 危险色 */ }
    }
  }
}
```

### 组件样式类

```css
/* 按钮变体 */
.btn-primary { @apply bg-primary-600 text-white hover:bg-primary-700; }
.btn-success { @apply bg-success-600 text-white hover:bg-success-700; }

/* 卡片组件 */
.card { @apply bg-white rounded-lg border border-gray-200 shadow-sm; }
```

## 🔌 后端集成

### API 端点配置

```typescript
// next.config.js
async rewrites() {
  return [
    {
      source: '/api/:path*',
      destination: 'http://localhost:8000/:path*'
    }
  ];
}
```

### 环境变量

```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
```

## 📱 响应式设计

- **移动优先**: 从小屏幕开始设计
- **断点系统**: sm, md, lg, xl 响应式断点
- **弹性布局**: Grid 和 Flexbox 布局系统
- **自适应组件**: 组件自动适配不同屏幕尺寸

## 🧪 测试

访问测试页面验证组件功能：

```
http://localhost:3000/test
```

## 📦 构建和部署

### 开发环境
```bash
pnpm dev          # 启动开发服务器
pnpm lint         # 代码检查
pnpm type-check   # 类型检查
```

### 生产环境
```bash
pnpm build        # 构建生产版本
pnpm start        # 启动生产服务器
```

## 🔄 版本对比

| 功能 | 原版本 | 新版本 |
|------|--------|--------|
| 框架 | Create React App | Next.js 15 |
| 样式 | 基础 CSS | Tailwind CSS |
| 状态管理 | useState | Zustand |
| 类型安全 | 部分 | 完整 TypeScript |
| 组件化 | 单体组件 | 模块化组件 |
| 响应式 | 基础 | 完全响应式 |

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**重构完成**: 从传统 React 应用成功迁移到现代化 Next.js 架构，提供更好的开发体验和用户体验。
