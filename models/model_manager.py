import asyncio
import aiohttp
import base64
import json
import logging
import os
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod
import io
from PIL import Image
from pathlib import Path

logger = logging.getLogger(__name__)

class BaseModel(ABC):
    """模型基类 - 符合OpenAI接口标准"""
    
    def __init__(self, config: Dict[str, Any]):
        self.name = config["name"]
        self.model_id = config["id"]
        self.provider = config.get("provider", "unknown")
        self.api_config = config.get("api_config", {})
        self.capabilities = config.get("capabilities", {})
        self.description = config.get("description", "")
        
        # 从环境变量获取API密钥
        api_key_env = self.api_config.get("api_key_env")
        self.api_key = os.getenv(api_key_env) if api_key_env else None
        
    @abstractmethod
    async def analyze_image(self, image_data: bytes, prompt: str) -> Dict[str, Any]:
        """分析图像 - 统一接口"""
        pass

    async def analyze_multimodal(self, image_data: bytes, prompt: str, audio_transcription: str = None) -> Dict[str, Any]:
        """多模态分析 - 图像+音频转录+文本提示词"""
        # 构建增强的提示词
        enhanced_prompt = self._build_multimodal_prompt(prompt, audio_transcription)

        # 调用图像分析方法
        result = await self.analyze_image(image_data, enhanced_prompt)

        # 添加多模态信息
        if result.get("success", False):
            result["multimodal"] = True
            result["audio_transcription"] = audio_transcription
            result["original_prompt"] = prompt
            result["enhanced_prompt"] = enhanced_prompt

        return result

    def _build_multimodal_prompt(self, visual_prompt: str, audio_transcription: str = None) -> str:
        """构建多模态提示词"""
        if not audio_transcription or not audio_transcription.strip():
            return visual_prompt

        # 构建包含音频信息的增强提示词
        enhanced_prompt = f"""请分析这张图片，并结合以下音频转录内容：

音频转录内容："{audio_transcription.strip()}"

视觉分析要求：{visual_prompt}

请综合考虑图像内容和音频信息，提供全面的分析结果。如果音频内容与图像内容相关，请特别说明它们之间的关联性。"""

        return enhanced_prompt
    
    def encode_image(self, image_data: bytes) -> str:
        """将图像编码为base64"""
        return base64.b64encode(image_data).decode('utf-8')
    
    def _create_openai_compatible_payload(self, image_data: bytes, prompt: str) -> Dict[str, Any]:
        """创建OpenAI兼容的请求负载"""
        base64_image = self.encode_image(image_data)
        
        return {
            "model": self.api_config.get("model", self.model_id),
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": self.api_config.get("max_tokens", 1000),
            "temperature": self.api_config.get("temperature", 0.7)
        }

class QwenModel(BaseModel):
    """Qwen模型实现"""
    
    async def analyze_image(self, image_data: bytes, prompt: str) -> Dict[str, Any]:
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = self._create_openai_compatible_payload(image_data, prompt)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_config['base_url']}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=self.api_config.get("timeout", 60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "text": result["choices"][0]["message"]["content"],
                            "model": self.name,
                            "success": True,
                            "provider": self.provider
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"{self.name} API错误: {response.status} - {error_text}")
                        return {
                            "text": f"分析失败: {error_text}",
                            "model": self.name,
                            "success": False,
                            "provider": self.provider
                        }
                        
        except Exception as e:
            logger.error(f"{self.name} 模型调用失败: {e}")
            return {
                "text": f"模型调用异常: {str(e)}",
                "model": self.name,
                "success": False,
                "provider": self.provider
            }

class MiniCPMModel(BaseModel):
    """MiniCPM模型实现"""
    
    async def analyze_image(self, image_data: bytes, prompt: str) -> Dict[str, Any]:
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = self._create_openai_compatible_payload(image_data, prompt)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_config['base_url']}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=self.api_config.get("timeout", 60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "text": result["choices"][0]["message"]["content"],
                            "model": self.name,
                            "success": True,
                            "provider": self.provider
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"{self.name} API错误: {response.status} - {error_text}")
                        return {
                            "text": f"分析失败: {error_text}",
                            "model": self.name,
                            "success": False,
                            "provider": self.provider
                        }
                        
        except Exception as e:
            logger.error(f"{self.name} 模型调用失败: {e}")
            return {
                "text": f"模型调用异常: {str(e)}",
                "model": self.name,
                "success": False,
                "provider": self.provider
            }

class DoubaoModel(BaseModel):
    """豆包模型实现"""
    
    async def analyze_image(self, image_data: bytes, prompt: str) -> Dict[str, Any]:
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = self._create_openai_compatible_payload(image_data, prompt)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_config['base_url']}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=self.api_config.get("timeout", 90)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "text": result["choices"][0]["message"]["content"],
                            "model": self.name,
                            "success": True,
                            "provider": self.provider
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"{self.name} API错误: {response.status} - {error_text}")
                        return {
                            "text": f"分析失败: {error_text}",
                            "model": self.name,
                            "success": False,
                            "provider": self.provider
                        }
                        
        except Exception as e:
            logger.error(f"{self.name} 模型调用失败: {e}")
            return {
                "text": f"模型调用异常: {str(e)}",
                "model": self.name,
                "success": False,
                "provider": self.provider
            }

class ChatGLMModel(BaseModel):
    """ChatGLM模型实现"""
    
    async def analyze_image(self, image_data: bytes, prompt: str) -> Dict[str, Any]:
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = self._create_openai_compatible_payload(image_data, prompt)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_config['base_url']}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=self.api_config.get("timeout", 60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "text": result["choices"][0]["message"]["content"],
                            "model": self.name,
                            "success": True,
                            "provider": self.provider
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"{self.name} API错误: {response.status} - {error_text}")
                        return {
                            "text": f"分析失败: {error_text}",
                            "model": self.name,
                            "success": False,
                            "provider": self.provider
                        }
                        
        except Exception as e:
            logger.error(f"{self.name} 模型调用失败: {e}")
            return {
                "text": f"模型调用异常: {str(e)}",
                "model": self.name,
                "success": False,
                "provider": self.provider
            }

class MockModel(BaseModel):
    """模拟模型（用于测试）"""
    
    async def analyze_image(self, image_data: bytes, prompt: str) -> Dict[str, Any]:
        # 模拟处理时间
        await asyncio.sleep(1)
        
        # 获取图像基本信息
        try:
            image = Image.open(io.BytesIO(image_data))
            width, height = image.size
            format_info = image.format or "Unknown"
            
            mock_response = f"""根据提示词"{prompt}"，我分析了这张图片：

图片基本信息：
- 尺寸: {width} x {height} 像素
- 格式: {format_info}

模拟分析结果：
这是一张清晰的图片，包含了丰富的视觉内容。图片整体构图良好，色彩饱和度适中。

[这是 {self.name} 模型的模拟响应，用于测试系统功能]

分析时间: {asyncio.get_event_loop().time()}"""
            
            return {
                "text": mock_response,
                "model": self.name,
                "success": True,
                "provider": self.provider,
                "image_info": {
                    "width": width,
                    "height": height,
                    "format": format_info
                }
            }
        except Exception as e:
            return {
                "text": f"模拟分析失败: {str(e)}",
                "model": self.name,
                "success": False,
                "provider": self.provider
            }

class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        self.video_models: Dict[str, BaseModel] = {}
        self.image_models: Dict[str, BaseModel] = {}
        self.model_stats: Dict[str, Dict] = {}
        self.config_loaded = False
        
    async def load_model_configs(self):
        """加载模型配置文件"""
        try:
            logger.info("开始加载模型配置...")
            
            # 加载视频模型配置
            video_config_path = Path("config/video_models.json")
            if video_config_path.exists():
                try:
                    with open(video_config_path, 'r', encoding='utf-8') as f:
                        video_config = json.load(f)
                        for model_config in video_config.get("models", []):
                            await self._create_model_instance(model_config, "video")
                    logger.info(f"从 {video_config_path} 加载了 {len(video_config.get('models', []))} 个视频模型")
                except Exception as e:
                    logger.error(f"加载视频模型配置失败: {e}")
            else:
                logger.warning(f"视频模型配置文件不存在: {video_config_path}")
            
            # 加载图片模型配置
            image_config_path = Path("config/image_models.json")
            if image_config_path.exists():
                try:
                    with open(image_config_path, 'r', encoding='utf-8') as f:
                        image_config = json.load(f)
                        for model_config in image_config.get("models", []):
                            await self._create_model_instance(model_config, "image")
                    logger.info(f"从 {image_config_path} 加载了 {len(image_config.get('models', []))} 个图片模型")
                except Exception as e:
                    logger.error(f"加载图片模型配置失败: {e}")
            else:
                logger.warning(f"图片模型配置文件不存在: {image_config_path}")
            
            # 如果没有加载到任何模型，添加Mock模型
            if len(self.video_models) == 0 and len(self.image_models) == 0:
                logger.warning("没有加载到任何模型配置，添加默认Mock模型")
                await self._add_mock_models()
            
            self.config_loaded = True
            logger.info(f"模型配置加载完成：{len(self.video_models)} 个视频模型，{len(self.image_models)} 个图片模型")
            
        except Exception as e:
            logger.error(f"加载模型配置失败: {e}")
            # 如果配置加载失败，至少添加Mock模型
            await self._add_mock_models()
            self.config_loaded = True
    
    async def _create_model_instance(self, config: Dict[str, Any], model_type: str):
        """根据配置创建模型实例"""
        try:
            model_id = config.get("id", config.get("name", "unknown"))
            provider = config.get("provider", "unknown")
            
            # 根据提供商创建相应的模型实例
            if "qwen" in model_id.lower() or provider == "alibaba":
                model = QwenModel(config)
            elif "minicpm" in model_id.lower() or provider == "openbmb":
                model = MiniCPMModel(config)
            elif "doubao" in model_id.lower() or provider == "bytedance":
                model = DoubaoModel(config)
            elif "chatglm" in model_id.lower() or provider == "zhipu":
                model = ChatGLMModel(config)
            elif provider == "openai_compatible":
                logger.info(f"创建 OpenAICompatibleModel 实例: {config['name']}")
                model = OpenAICompatibleModel(config)
            else:
                # 默认使用Mock模型
                model = MockModel(config)
            
            # 根据类型存储模型
            if model_type == "video":
                self.video_models[model_id] = model
            else:
                self.image_models[model_id] = model
            
            # 初始化统计信息
            self.model_stats[model_id] = {
                "calls": 0,
                "success": 0,
                "errors": 0,
                "avg_response_time": 0,
                "type": model_type
            }
            
            logger.info(f"创建模型实例成功: {model_id} ({model_type})")
            
        except Exception as e:
            logger.error(f"创建模型实例失败: {e}")
    
    async def _add_mock_models(self):
        """添加Mock模型用于测试"""
        mock_video_config = {
            "name": "Mock Video Model",
            "id": "mock-video-model",
            "provider": "mock",
            "api_config": {
                "model": "mock-video",
                "max_tokens": 1000,
                "temperature": 0.7
            },
            "description": "模拟视频分析模型，用于测试系统功能"
        }
        
        mock_image_config = {
            "name": "Mock Image Model", 
            "id": "mock-image-model",
            "provider": "mock",
            "api_config": {
                "model": "mock-image",
                "max_tokens": 1000,
                "temperature": 0.7
            },
            "description": "模拟图片分析模型，用于测试系统功能"
        }
        
        await self._create_model_instance(mock_video_config, "video")
        await self._create_model_instance(mock_image_config, "image")
        
        logger.info("已添加Mock模型用于测试")
    
    def get_video_models(self) -> List[Dict[str, str]]:
        """获取视频模型列表"""
        return [
            {"id": model_id, "name": model.name}
            for model_id, model in self.video_models.items()
        ]
    
    def get_image_models(self) -> List[Dict[str, str]]:
        """获取图片模型列表"""
        return [
            {"id": model_id, "name": model.name}
            for model_id, model in self.image_models.items()
        ]
    
    async def analyze_with_video_model(
        self, 
        model_id: str, 
        image_data: bytes, 
        prompt: str
    ) -> Dict[str, Any]:
        """使用视频模型分析图像"""
        return await self._analyze_with_model(model_id, image_data, prompt, self.video_models)
    
    async def analyze_with_image_model(
        self,
        model_id: str,
        image_data: bytes,
        prompt: str
    ) -> Dict[str, Any]:
        """使用图片模型分析图像"""
        return await self._analyze_with_model(model_id, image_data, prompt, self.image_models)

    async def analyze_with_multimodal(
        self,
        model_id: str,
        image_data: bytes,
        prompt: str,
        audio_transcription: str = None,
        model_type: str = "video"
    ) -> Dict[str, Any]:
        """多模态分析 - 支持图像+音频转录+文本提示词"""
        model_dict = self.video_models if model_type == "video" else self.image_models

        if model_id not in model_dict:
            return {
                "text": f"模型 {model_id} 不存在",
                "model": model_id,
                "success": False,
                "multimodal": True
            }

        model = model_dict[model_id]
        stats = self.model_stats[model_id]

        start_time = asyncio.get_event_loop().time()

        try:
            stats["calls"] += 1

            # 使用多模态分析方法
            result = await model.analyze_multimodal(image_data, prompt, audio_transcription)

            if result.get("success", False):
                stats["success"] += 1
            else:
                stats["errors"] += 1

            # 更新平均响应时间
            response_time = asyncio.get_event_loop().time() - start_time
            stats["avg_response_time"] = (
                (stats["avg_response_time"] * (stats["calls"] - 1) + response_time)
                / stats["calls"]
            )

            result["response_time"] = response_time
            result["model_type"] = model_type
            return result

        except Exception as e:
            stats["errors"] += 1
            logger.error(f"多模态模型 {model_id} 调用失败: {e}")
            return {
                "text": f"多模态模型调用失败: {str(e)}",
                "model": model.name if model else model_id,
                "success": False,
                "multimodal": True
            }
    
    async def _analyze_with_model(
        self,
        model_id: str,
        image_data: bytes,
        prompt: str,
        model_dict: Dict[str, BaseModel]
    ) -> Dict[str, Any]:
        """通用的模型分析方法"""
        if model_id not in model_dict:
            return {
                "text": f"模型 {model_id} 不存在",
                "model": model_id,
                "success": False
            }
        
        model = model_dict[model_id]
        stats = self.model_stats[model_id]
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            stats["calls"] += 1
            result = await model.analyze_image(image_data, prompt)
            
            if result.get("success", False):
                stats["success"] += 1
            else:
                stats["errors"] += 1
                
            # 更新平均响应时间
            response_time = asyncio.get_event_loop().time() - start_time
            stats["avg_response_time"] = (
                (stats["avg_response_time"] * (stats["calls"] - 1) + response_time) 
                / stats["calls"]
            )
            
            result["response_time"] = response_time
            return result
            
        except Exception as e:
            stats["errors"] += 1
            logger.error(f"模型 {model_id} 调用失败: {e}")
            return {
                "text": f"模型调用失败: {str(e)}",
                "model": model.name if model else model_id,
                "success": False
            }
    
    def get_model_stats(self) -> Dict[str, Dict]:
        """获取模型统计信息"""
        return self.model_stats.copy()
    
    async def health_check(self, model_id: str, model_type: str = "image") -> Dict[str, Any]:
        """检查模型健康状态"""
        model_dict = self.video_models if model_type == "video" else self.image_models
        
        if model_id not in model_dict:
            return {"healthy": False, "error": "模型不存在"}
        
        try:
            # 使用1x1像素的测试图片
            test_image = Image.new('RGB', (1, 1), color='white')
            buffer = io.BytesIO()
            test_image.save(buffer, format='JPEG')
            test_data = buffer.getvalue()
            
            result = await self._analyze_with_model(
                model_id, 
                test_data, 
                "测试连接",
                model_dict
            )
            
            return {
                "healthy": result.get("success", False),
                "response_time": result.get("response_time", 0),
                "error": None if result.get("success") else result.get("text")
            }
            
        except Exception as e:
            return {"healthy": False, "error": str(e)}
    
    # 兼容旧版接口的方法
    async def add_model(self, model_config: Dict[str, Any]):
        """添加模型（兼容旧版）"""
        await self._create_model_instance(model_config, "video")
    
    def get_available_models(self) -> List[str]:
        """获取所有可用模型列表（兼容旧版）"""
        all_models = []
        for model in self.get_video_models():
            all_models.append(model["name"])
        for model in self.get_image_models():
            if model["name"] not in all_models:
                all_models.append(model["name"])
        return all_models
    
    async def analyze_with_model(self, model_name: str, image_data: bytes, prompt: str) -> Dict[str, Any]:
        """使用模型分析（兼容旧版）"""
        # 先尝试在视频模型中查找
        for model_id, model in self.video_models.items():
            if model.name == model_name or model_id == model_name:
                return await self.analyze_with_video_model(model_id, image_data, prompt)
        
        # 再尝试在图片模型中查找
        for model_id, model in self.image_models.items():
            if model.name == model_name or model_id == model_name:
                return await self.analyze_with_image_model(model_id, image_data, prompt)
        
        # 如果都找不到，返回错误
        return {
            "text": f"模型 {model_name} 不存在",
            "model": model_name,
            "success": False
        }