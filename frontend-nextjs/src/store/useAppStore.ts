import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import type {
  AppState,
  VideoSession,
  AudioStatus,
  TranscriptionResult,
  MultimodalAnalysisResult,
  ModelInfo,
  ConnectionStatus,
} from '@/types';

interface AppStore extends AppState {
  // 视频操作
  addVideoSession: (session: VideoSession) => void;
  removeVideoSession: (sessionId: string) => void;
  setCurrentSession: (sessionId: string | null) => void;
  updateVideoSession: (sessionId: string, updates: Partial<VideoSession>) => void;
  updateVideoFrame: (sessionId: string, frameData: string) => void;
  
  // 音频操作
  setAudioStatus: (sessionId: string, status: AudioStatus) => void;
  addTranscriptionResult: (sessionId: string, result: TranscriptionResult) => void;
  clearTranscriptionHistory: (sessionId: string) => void;
  
  // 模型操作
  setAvailableModels: (models: ModelInfo[]) => void;
  setSelectedModel: (modelId: string | null) => void;
  
  // 分析结果操作
  addAnalysisResult: (result: MultimodalAnalysisResult) => void;
  clearAnalysisResults: () => void;
  
  // UI 状态操作
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // WebSocket 状态操作
  setWsConnected: (connected: boolean) => void;
  setWsReconnecting: (reconnecting: boolean) => void;
  
  // 工具方法
  getCurrentSession: () => VideoSession | null;
  getAudioStatus: (sessionId: string) => AudioStatus | null;
  getTranscriptionHistory: (sessionId: string) => TranscriptionResult[];
}

const initialState: AppState = {
  videoSessions: {},
  currentSessionId: null,
  audioStatus: {},
  transcriptionHistory: {},
  availableModels: [],
  selectedModel: null,
  analysisResults: [],
  isLoading: false,
  error: null,
  wsConnected: false,
  wsReconnecting: false,
};

export const useAppStore = create<AppStore>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      ...initialState,
      
      // 视频操作
      addVideoSession: (session) =>
        set((state) => ({
          videoSessions: {
            ...state.videoSessions,
            [session.id]: session,
          },
        }), false, 'addVideoSession'),
      
      removeVideoSession: (sessionId) =>
        set((state) => {
          const { [sessionId]: removed, ...rest } = state.videoSessions;
          const { [sessionId]: removedAudio, ...restAudio } = state.audioStatus;
          const { [sessionId]: removedHistory, ...restHistory } = state.transcriptionHistory;
          
          return {
            videoSessions: rest,
            audioStatus: restAudio,
            transcriptionHistory: restHistory,
            currentSessionId: state.currentSessionId === sessionId ? null : state.currentSessionId,
          };
        }, false, 'removeVideoSession'),
      
      setCurrentSession: (sessionId) =>
        set({ currentSessionId: sessionId }, false, 'setCurrentSession'),
      
      updateVideoSession: (sessionId, updates) =>
        set((state) => ({
          videoSessions: {
            ...state.videoSessions,
            [sessionId]: {
              ...state.videoSessions[sessionId],
              ...updates,
            },
          },
        }), false, 'updateVideoSession'),

      updateVideoFrame: (sessionId, frameData) =>
        set((state) => ({
          videoSessions: {
            ...state.videoSessions,
            [sessionId]: {
              ...state.videoSessions[sessionId],
              currentFrame: frameData,
            },
          },
        }), false, 'updateVideoFrame'),
      
      // 音频操作
      setAudioStatus: (sessionId, status) =>
        set((state) => ({
          audioStatus: {
            ...state.audioStatus,
            [sessionId]: status,
          },
        }), false, 'setAudioStatus'),
      
      addTranscriptionResult: (sessionId, result) =>
        set((state) => {
          const currentHistory = state.transcriptionHistory[sessionId] || [];
          const maxHistory = 50; // 最多保留50条记录
          
          const newHistory = [result, ...currentHistory].slice(0, maxHistory);
          
          return {
            transcriptionHistory: {
              ...state.transcriptionHistory,
              [sessionId]: newHistory,
            },
          };
        }, false, 'addTranscriptionResult'),
      
      clearTranscriptionHistory: (sessionId) =>
        set((state) => ({
          transcriptionHistory: {
            ...state.transcriptionHistory,
            [sessionId]: [],
          },
        }), false, 'clearTranscriptionHistory'),
      
      // 模型操作
      setAvailableModels: (models) =>
        set({ availableModels: models }, false, 'setAvailableModels'),
      
      setSelectedModel: (modelId) =>
        set({ selectedModel: modelId }, false, 'setSelectedModel'),
      
      // 分析结果操作
      addAnalysisResult: (result) =>
        set((state) => {
          const maxResults = 20; // 最多保留20条结果
          const newResults = [result, ...state.analysisResults].slice(0, maxResults);
          
          return { analysisResults: newResults };
        }, false, 'addAnalysisResult'),
      
      clearAnalysisResults: () =>
        set({ analysisResults: [] }, false, 'clearAnalysisResults'),
      
      // UI 状态操作
      setLoading: (loading) =>
        set({ isLoading: loading }, false, 'setLoading'),
      
      setError: (error) =>
        set({ error }, false, 'setError'),
      
      // WebSocket 状态操作
      setWsConnected: (connected) =>
        set({ wsConnected: connected }, false, 'setWsConnected'),
      
      setWsReconnecting: (reconnecting) =>
        set({ wsReconnecting: reconnecting }, false, 'setWsReconnecting'),
      
      // 工具方法
      getCurrentSession: () => {
        const state = get();
        return state.currentSessionId ? state.videoSessions[state.currentSessionId] || null : null;
      },
      
      getAudioStatus: (sessionId) => {
        const state = get();
        return state.audioStatus[sessionId] || null;
      },
      
      getTranscriptionHistory: (sessionId) => {
        const state = get();
        return state.transcriptionHistory[sessionId] || [];
      },
    })),
    {
      name: 'video-analysis-store',
    }
  )
);

// 选择器 hooks
export const useVideoSessions = () => useAppStore((state) => state.videoSessions);
export const useCurrentSession = () => useAppStore((state) => state.getCurrentSession());
export const useCurrentSessionId = () => useAppStore((state) => state.currentSessionId);
export const useAudioStatus = (sessionId: string) => 
  useAppStore((state) => state.getAudioStatus(sessionId));
export const useTranscriptionHistory = (sessionId: string) => 
  useAppStore((state) => state.getTranscriptionHistory(sessionId));
export const useAvailableModels = () => useAppStore((state) => state.availableModels);
export const useSelectedModel = () => useAppStore((state) => state.selectedModel);
export const useAnalysisResults = () => useAppStore((state) => state.analysisResults);
export const useAppLoading = () => useAppStore((state) => state.isLoading);
export const useAppError = () => useAppStore((state) => state.error);
export const useWsStatus = () => useAppStore((state) => ({
  connected: state.wsConnected,
  reconnecting: state.wsReconnecting,
}));
