import { config } from '@/lib/config';
import type {
  WebSocketMessage,
  FrameAnalysisMessage,
  TranscriptionMessage,
  MultimodalAnalysisMessage,
  ConnectionStatus,
} from '@/types';

type MessageHandler = (message: WebSocketMessage) => void;
type ConnectionHandler = (status: ConnectionStatus) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts: number;
  private reconnectInterval: number;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private messageHandlers: Set<MessageHandler> = new Set();
  private connectionHandlers: Set<ConnectionHandler> = new Set();
  private isManualClose = false;

  constructor() {
    this.url = config.wsUrl;
    this.maxReconnectAttempts = config.maxReconnectAttempts;
    this.reconnectInterval = config.reconnectInterval;
  }

  connect(): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    this.isManualClose = false;
    this.notifyConnectionHandlers('connecting');

    try {
      this.ws = new WebSocket(this.url);
      this.setupEventListeners();
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      this.notifyConnectionHandlers('error');
      this.scheduleReconnect();
    }
  }

  disconnect(): void {
    this.isManualClose = true;
    this.clearReconnectTimer();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.notifyConnectionHandlers('disconnected');
  }

  send(message: any): boolean {
    if (this.ws?.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        return false;
      }
    }
    return false;
  }

  addMessageHandler(handler: MessageHandler): () => void {
    this.messageHandlers.add(handler);
    return () => this.messageHandlers.delete(handler);
  }

  addConnectionHandler(handler: ConnectionHandler): () => void {
    this.connectionHandlers.add(handler);
    return () => this.connectionHandlers.delete(handler);
  }

  getConnectionStatus(): ConnectionStatus {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'error';
    }
  }

  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
      this.clearReconnectTimer();
      this.notifyConnectionHandlers('connected');
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        const message: WebSocketMessage = {
          ...data,
          timestamp: new Date(data.timestamp || Date.now()),
        };
        
        this.notifyMessageHandlers(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      this.ws = null;
      
      if (!this.isManualClose) {
        this.notifyConnectionHandlers('disconnected');
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.warn('WebSocket connection failed - backend server may not be running');
      this.notifyConnectionHandlers('error');
    };
  }

  private scheduleReconnect(): void {
    if (this.isManualClose || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnect attempts reached or manual close');
      return;
    }

    this.clearReconnectTimer();
    this.reconnectAttempts++;
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
    
    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, this.reconnectInterval);
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private notifyMessageHandlers(message: WebSocketMessage): void {
    this.messageHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('Error in message handler:', error);
      }
    });
  }

  private notifyConnectionHandlers(status: ConnectionStatus): void {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(status);
      } catch (error) {
        console.error('Error in connection handler:', error);
      }
    });
  }
}

// 创建单例实例
export const wsService = new WebSocketService();

// 类型守卫函数
export function isFrameAnalysisMessage(message: WebSocketMessage): message is FrameAnalysisMessage {
  return message.type === 'frame_analysis';
}

export function isTranscriptionMessage(message: WebSocketMessage): message is TranscriptionMessage {
  return message.type === 'transcription_result';
}

export function isMultimodalAnalysisMessage(message: WebSocketMessage): message is MultimodalAnalysisMessage {
  return message.type === 'multimodal_analysis_result';
}

// Hook for using WebSocket in components
export function useWebSocket() {
  return {
    connect: () => wsService.connect(),
    disconnect: () => wsService.disconnect(),
    send: (message: any) => wsService.send(message),
    addMessageHandler: (handler: MessageHandler) => wsService.addMessageHandler(handler),
    addConnectionHandler: (handler: ConnectionHandler) => wsService.addConnectionHandler(handler),
    getConnectionStatus: () => wsService.getConnectionStatus(),
  };
}
