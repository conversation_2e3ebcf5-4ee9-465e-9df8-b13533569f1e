import asyncio
import threading
import time
import logging
import os
import tempfile
import subprocess
import queue
from typing import Optional, Dict, Any, Callable
from pathlib import Path
import numpy as np
import soundfile as sf
import librosa
from collections import deque
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class SenseVoiceTranscriber:
    """SenseVoice语音转录器"""
    
    def __init__(self, model_path: str = "models/SenseVoiceSmall"):
        self.model_path = model_path
        self.model = None
        self.is_initialized = False
        
    def initialize(self):
        """初始化SenseVoice模型"""
        try:
            from funasr import AutoModel
            
            logger.info(f"正在初始化SenseVoice模型: {self.model_path}")
            
            self.model = AutoModel(
                model=self.model_path,
                vad_model="fsmn-vad",
                vad_kwargs={"max_single_segment_time": 30000},
                hub="hf",
                device="cpu"  # 可以根据需要改为cuda
            )
            
            self.is_initialized = True
            logger.info("SenseVoice模型初始化成功")
            
        except Exception as e:
            logger.error(f"SenseVoice模型初始化失败: {e}")
            self.is_initialized = False
            
    def transcribe_audio(self, audio_file_path: str, language: str = "auto") -> Dict[str, Any]:
        """转录音频文件"""
        if not self.is_initialized:
            return {
                "success": False,
                "text": "",
                "error": "模型未初始化"
            }
            
        try:
            # 使用SenseVoice进行转录
            result = self.model.generate(
                input=audio_file_path,
                cache={},
                language=language,  # "auto", "zh", "en", "yue", "ja", "ko"
                use_itn=True,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=15,
            )
            
            if result and len(result) > 0:
                from funasr.utils.postprocess_utils import rich_transcription_postprocess
                text = rich_transcription_postprocess(result[0]["text"])
                
                return {
                    "success": True,
                    "text": text,
                    "language": language,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "text": "",
                    "error": "转录结果为空"
                }
                
        except Exception as e:
            logger.error(f"音频转录失败: {e}")
            return {
                "success": False,
                "text": "",
                "error": str(e)
            }

class AudioExtractor:
    """音频提取器 - 从RTSP流中提取音频"""
    
    def __init__(self, rtsp_url: str, session_id: str):
        self.rtsp_url = rtsp_url
        self.session_id = session_id
        self.is_running = False
        self.process = None
        self.temp_dir = None
        self.audio_queue = queue.Queue(maxsize=100)
        self.thread = None
        
    def _check_audio_stream(self) -> bool:
        """检查RTSP流是否包含音频轨道"""
        try:
            cmd = [
                'ffprobe',
                '-hide_banner',
                '-loglevel', 'error',
                '-rtsp_transport', 'tcp',
                '-select_streams', 'a:0',
                '-show_entries', 'stream=codec_type',
                '-of', 'csv=p=0',
                self.rtsp_url
            ]

            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10
            )

            return result.returncode == 0 and 'audio' in result.stdout

        except Exception as e:
            logger.debug(f"音频轨道检测失败: {e}")
            return False

    def start(self) -> bool:
        """开始音频提取"""
        if self.is_running:
            logger.warning("音频提取器已在运行")
            return True

        # 首先检查是否有音频轨道
        if not self._check_audio_stream():
            logger.info(f"RTSP流中未检测到音频轨道，跳过音频提取: {self.rtsp_url}")
            return False

        try:
            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix=f"audio_{self.session_id}_")
            logger.info(f"创建音频临时目录: {self.temp_dir}")

            # 启动音频提取线程
            self.thread = threading.Thread(target=self._extract_audio_loop)
            self.thread.daemon = True
            self.is_running = True
            self.thread.start()

            logger.info(f"音频提取器启动成功: {self.rtsp_url}")
            return True

        except Exception as e:
            logger.error(f"启动音频提取器失败: {e}")
            self.stop()
            return False
    
    def stop(self):
        """停止音频提取"""
        self.is_running = False
        
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
            self.process = None
            
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
            
        # 清理临时目录
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
                logger.info(f"清理音频临时目录: {self.temp_dir}")
            except Exception as e:
                logger.warning(f"清理临时目录失败: {e}")
                
        logger.info("音频提取器已停止")
    
    def _extract_audio_loop(self):
        """音频提取循环"""
        segment_duration = 5  # 每5秒一个音频片段
        segment_count = 0
        
        while self.is_running:
            try:
                segment_count += 1
                output_file = os.path.join(self.temp_dir, f"audio_segment_{segment_count:04d}.wav")
                
                # 使用FFmpeg提取音频片段
                cmd = [
                    'ffmpeg',
                    '-hide_banner',
                    '-loglevel', 'error',
                    '-rtsp_transport', 'tcp',
                    '-i', self.rtsp_url,
                    '-vn',  # 不处理视频
                    '-acodec', 'pcm_s16le',  # 转换为PCM格式
                    '-ar', '16000',  # 采样率16kHz (SenseVoice要求)
                    '-ac', '1',  # 单声道
                    '-t', str(segment_duration),  # 持续时间
                    '-y',  # 覆盖输出文件
                    output_file
                ]
                
                logger.debug(f"提取音频片段: {output_file}")
                
                self.process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                
                stdout, stderr = self.process.communicate()
                
                if self.process.returncode == 0 and os.path.exists(output_file):
                    # 检查文件大小
                    file_size = os.path.getsize(output_file)
                    if file_size > 1024:  # 至少1KB
                        # 将音频文件路径加入队列
                        try:
                            self.audio_queue.put(output_file, timeout=1)
                            logger.debug(f"音频片段已加入队列: {output_file}")
                        except queue.Full:
                            logger.warning("音频队列已满，丢弃旧片段")
                            # 移除队列中最老的文件
                            try:
                                old_file = self.audio_queue.get_nowait()
                                if os.path.exists(old_file):
                                    os.remove(old_file)
                                self.audio_queue.put(output_file, timeout=1)
                            except:
                                pass
                    else:
                        logger.warning(f"音频片段太小，跳过: {output_file}")
                        if os.path.exists(output_file):
                            os.remove(output_file)
                else:
                    logger.warning(f"音频提取失败: {stderr.decode() if stderr else '未知错误'}")
                    
            except Exception as e:
                logger.error(f"音频提取循环错误: {e}")
                time.sleep(1)  # 错误后等待一秒再重试
                
        logger.info("音频提取循环结束")
    
    def get_latest_audio(self) -> Optional[str]:
        """获取最新的音频文件"""
        try:
            return self.audio_queue.get_nowait()
        except queue.Empty:
            return None

class AudioStreamService:
    """音频流服务 - 管理音频提取和转录"""
    
    def __init__(self):
        self.active_streams: Dict[str, AudioExtractor] = {}
        self.transcriber = SenseVoiceTranscriber()
        self.transcription_callbacks: Dict[str, Callable] = {}
        self.transcription_threads: Dict[str, threading.Thread] = {}
        
    async def initialize(self):
        """初始化音频服务"""
        logger.info("初始化音频服务...")
        
        # 在后台线程中初始化SenseVoice模型
        def init_model():
            self.transcriber.initialize()
            
        init_thread = threading.Thread(target=init_model)
        init_thread.daemon = True
        init_thread.start()
        
        logger.info("音频服务初始化完成")

    async def start_audio_stream(self, rtsp_url: str, session_id: str) -> bool:
        """开始音频流处理"""
        if session_id in self.active_streams:
            logger.warning(f"音频流 {session_id} 已存在")
            return True

        try:
            # 创建音频提取器
            extractor = AudioExtractor(rtsp_url, session_id)

            if extractor.start():
                self.active_streams[session_id] = extractor
                logger.info(f"音频流 {session_id} 启动成功")
                return True
            else:
                logger.error(f"音频流 {session_id} 启动失败")
                return False

        except Exception as e:
            logger.error(f"启动音频流失败: {e}")
            return False

    async def stop_audio_stream(self, session_id: str):
        """停止音频流处理"""
        if session_id in self.active_streams:
            extractor = self.active_streams[session_id]
            extractor.stop()
            del self.active_streams[session_id]

            # 停止转录线程
            if session_id in self.transcription_threads:
                thread = self.transcription_threads[session_id]
                if thread.is_alive():
                    # 线程会在extractor停止后自然结束
                    thread.join(timeout=5)
                del self.transcription_threads[session_id]

            # 移除回调
            if session_id in self.transcription_callbacks:
                del self.transcription_callbacks[session_id]

            logger.info(f"音频流 {session_id} 已停止")

    def start_transcription(self, session_id: str, callback: Callable[[Dict[str, Any]], None], language: str = "auto"):
        """开始实时转录"""
        if session_id not in self.active_streams:
            logger.error(f"音频流 {session_id} 不存在")
            return False

        if not self.transcriber.is_initialized:
            logger.error("SenseVoice模型未初始化")
            return False

        # 保存回调函数
        self.transcription_callbacks[session_id] = callback

        # 启动转录线程
        def transcription_loop():
            extractor = self.active_streams.get(session_id)
            if not extractor:
                return

            logger.info(f"开始转录音频流 {session_id}")

            while extractor.is_running and session_id in self.active_streams:
                try:
                    # 获取最新音频文件
                    audio_file = extractor.get_latest_audio()

                    if audio_file and os.path.exists(audio_file):
                        logger.debug(f"转录音频文件: {audio_file}")

                        # 进行转录
                        result = self.transcriber.transcribe_audio(audio_file, language)

                        # 添加会话信息
                        result["session_id"] = session_id
                        result["audio_file"] = audio_file

                        # 调用回调函数
                        if session_id in self.transcription_callbacks:
                            try:
                                self.transcription_callbacks[session_id](result)
                            except Exception as e:
                                logger.error(f"转录回调函数执行失败: {e}")

                        # 清理音频文件
                        try:
                            os.remove(audio_file)
                            logger.debug(f"清理音频文件: {audio_file}")
                        except Exception as e:
                            logger.warning(f"清理音频文件失败: {e}")
                    else:
                        # 没有新音频，等待一下
                        time.sleep(0.5)

                except Exception as e:
                    logger.error(f"转录循环错误: {e}")
                    time.sleep(1)

            logger.info(f"转录线程结束: {session_id}")

        thread = threading.Thread(target=transcription_loop)
        thread.daemon = True
        self.transcription_threads[session_id] = thread
        thread.start()

        return True

    def stop_transcription(self, session_id: str):
        """停止转录"""
        if session_id in self.transcription_callbacks:
            del self.transcription_callbacks[session_id]

        if session_id in self.transcription_threads:
            thread = self.transcription_threads[session_id]
            if thread.is_alive():
                # 线程会在回调被移除后自然结束
                thread.join(timeout=5)
            del self.transcription_threads[session_id]

        logger.info(f"转录已停止: {session_id}")

    def get_stream_status(self, session_id: str) -> Dict[str, Any]:
        """获取音频流状态"""
        if session_id not in self.active_streams:
            return {
                "exists": False,
                "running": False
            }

        extractor = self.active_streams[session_id]
        return {
            "exists": True,
            "running": extractor.is_running,
            "transcription_active": session_id in self.transcription_callbacks,
            "model_initialized": self.transcriber.is_initialized,
            "queue_size": extractor.audio_queue.qsize() if extractor.audio_queue else 0
        }

    def get_all_streams(self) -> Dict[str, Dict[str, Any]]:
        """获取所有音频流状态"""
        return {
            session_id: self.get_stream_status(session_id)
            for session_id in self.active_streams.keys()
        }

    async def cleanup(self):
        """清理所有音频流"""
        logger.info("清理所有音频流...")

        # 停止所有音频流
        for session_id in list(self.active_streams.keys()):
            await self.stop_audio_stream(session_id)

        logger.info("音频流清理完成")

# 全局音频服务实例
audio_service = AudioStreamService()
